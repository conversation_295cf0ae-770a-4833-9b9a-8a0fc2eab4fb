//------------------------------------------------------------------------
// 简称: RedRover_L
// 名称: 基于K线加权均值的支撑阻力线突破系统多 
// 类别: 公式应用
// 类型: 内建应用
// 输出:
//------------------------------------------------------------------------
//----------------------------------------------------------------------//
// 策略说明:
//			 本策略是基于K线加权均值的支撑阻力线突破系统
//			 
// 系统要素:
//			 1. K线的加权均值 = (最高价+最低价+2*收盘价)/4
//			 2. 支撑线 = K线加权均值 - ( 最高价 - K线加权均值)
//			 3. 阻力线 = K线加权均值 + ( K线加权均值 - 最低价)
// 入场条件:
//			 1. 当价格向上突破阻力线做多
//			 2. 当价格向下突破支撑线做空
// 出场条件: 
//			 1. 趋势反转即反向突破时出场
//			 2. 基于ATR的一定倍数的止盈
//
//		 注: 当前策略仅为做多系统, 如需做空, 请参见CL_RedRover_S
//----------------------------------------------------------------------//
Params
    Numeric ATRs(16);				// 几倍ATR止盈
    Numeric ATRLength(72);			// ATR周期
    Numeric VolLen(72);				// 成交量均值周期
    Numeric VolRatio(1.2);			// 成交量过滤倍数
    Numeric MinATR(0.1);			// ATR过滤阈值
    Bool ShowBreakout(true);		// 是否显示突破点
    Bool ShowExitPoints(true);		// 是否显示出场点
    
Vars
    Series<Numeric> WAvgPrice;		// K线加权均值
    Series<Numeric> Resistance;		// 阻力线
    Series<Numeric> Support;		// 支撑线
    Numeric ATRVal;					// ATR(平均真实波幅)
    Series<Numeric> myExitPrice;	// 开仓BAR根据当时的ATR计算出的止盈价
    Numeric VolMA;					// 成交量均值
    
Events
    OnBar(ArrayRef<Integer> indexs)
    {
        // 计算当前K线的加权均值、阻力线和支撑线
        WAvgPrice = (High + Low + (Close * 2)) / 4;
        Resistance = (WAvgPrice * 2) - Low;
        Support = (WAvgPrice * 2) - High;
        
        // 输出指标 - 增强绘图效果
        PlotNumeric("阻力线", Resistance[1]);
        PlotNumeric("支撑线", Support[1]);
        PlotNumeric("加权均值", WAvgPrice);
        
        // 计算ATR
        ATRVal = AvgTrueRange(ATRLength);	

        // 计算成交量均值
        VolMA = SMA(Vol, VolLen);

        // 开仓（增加成交量过滤和ATR过滤）
        If(MarketPosition == 0 
            And Close > Resistance[1] + MinMove * PriceScale 
            And Vol > 0
            And Vol > VolMA * VolRatio	// 成交量过滤条件
            And ATRVal > MinATR			// ATR过滤条件
        )
        {
            Buy(0, Max(Open,Resistance[1] + MinMove * PriceScale));
            
            // 突破点标记 - 使用Commentary代替
            If(ShowBreakout)
            {
                Commentary("突破买入");
            }
        }
        
        // 开仓时根据开仓BAR的ATR计算止盈价
        If(MarketPosition == 1 And BarsSinceEntry == 0)
        {
            myExitPrice = EntryPrice + ATRVal * ATRs;
            
            // 绘制止盈目标线
            PlotNumeric("止盈目标", myExitPrice);
        }
            
        // 平仓
        If(MarketPosition == 1 And BarsSinceEntry > 0 And Vol > 0)
        {
            // 止盈出场
            If(High >= myExitPrice)
            {
                Sell(0, Max(Open,myExitPrice));
                Commentary("止盈出场");
            }
            // 反向突破止损出场
            Else If(Close < Support[1] - MinMove * PriceScale)
            {
                Sell(0, Min(Open,Support[1] - MinMove * PriceScale));
                Commentary("反转出场");
            }
        }
    }
//------------------------------------------------------------------------
// 编译版本	GS2014.10.25
// 版权所有	TradeBlazer Software 2003－2025
// 更改声明	TradeBlazer Software保留对TradeBlazer平
//			台每一版本的TradeBlazer公式修改和重写的权利
//------------------------------------------------------------------------