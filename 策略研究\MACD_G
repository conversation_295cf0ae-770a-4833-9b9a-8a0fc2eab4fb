//------------------------------------------------------------------------
// 简称: MACD_G
// 名称: MACD_G
// 类别: 公式应用
// 类型: 用户应用
// 输出: Void
//------------------------------------------------------------------------
/*策略思想：

1.使用变量将MACD的快慢均线交叉点记录，然后获取上次交叉到本次交叉之间的

周期数。计算此周期内的高低点，当价格突破交叉后的高低点时进场，当MACD金叉(死叉)时

出场；
2.当MACD出现顶底背离时，开多开空；

核心计算：1.MACD交叉状态记录
	     2.构建周期内的高低点区间
		 3.描述背离状态，同时保存结果；
		 
多头进场：1.最近一个MACD信号是金叉，突破周期内高点；
		 2. 出现底背离时，开多；
空头进场：1.最近一个MACD信号是死叉，突破周期内低点；
		 2.出现顶背离时，开空；
*/
//使用周期：1小时周期

Params
	//默认螺纹参数
	Numeric N(2);
	Numeric X(5);
	Numeric TRS(25);//跟踪止损 
Vars
	Series<Numeric> MACDValue; 
	Series<Numeric> AvgMACD;
	Series<Bool> Cross_up;
	Series<Bool> Cross_dn;
	Series<Numeric> Hbar;
	Series<Numeric> Lbar;
	Series<Numeric> Highup;
	Series<Numeric> Lowdown;
	Series<Numeric> K(0);
	Series<Numeric> HD;
	Series<Numeric> LD;
	
	Series<Numeric> Myprice2;
	Series<Numeric> Myprice3;
	Series<Numeric> liQKA;
	Series<Numeric> DliqPoint;
	Series<Numeric> KliqPoint;
	Series<Numeric> HighAfterEntry;
	Series<Numeric> LowAfterEntry;
	
	Series<Numeric> Bar_Highup;
	Series<Numeric> Bar_Lowdown;
	Series<Numeric> Bar_HD;
	Series<Numeric> Bar_LD;
	Series<Numeric> RHbar;
	Series<Numeric> RLbar;
	Series<Numeric> D_MACD;
	Series<Numeric> K_MACD;
	Series<Numeric> RD_MACD;
	Series<Numeric> RK_MACD;
	Series<Numeric> RHD;
	Series<Numeric> RLD;
	Bool condtion_D;
	Bool condtion_K;
Events
    onBar(ArrayRef<Integer> indexs)
    {    
    		//记录开仓后高低点
        If(BarsSinceentry == 0)
        {
            HighAfterEntry = High;
            LowAfterEntry = Low;
        }else
        {
            HighAfterEntry = Min(HighAfterEntry,High); // 空头止损，更新最低的最高价
            LowAfterEntry = Max(LowAfterEntry,Low);    // 多头止损，更新最高的最低价
        }
    	
    	MACDValue = XAverage( Close, 4*N ) - XAverage( Close, 9*N ) ;	//快线
    	AvgMACD = XAverage(MACDValue,3*N);//慢线
    	
    	Cross_up=CrossOver(MACDValue,AvgMACD);//金叉
    	Cross_dn=CrossUnder(MACDValue,AvgMACD);//死叉
    
    	If(Cross_up[1])
    	{
    		RHbar=Hbar;//保存上一次交叉时的位置
    		Hbar=CurrentBar;//记录交叉时的位置
    	}
    	If(Cross_dn[1]) 
    	{
    		RLbar=Lbar;//保存上一次交叉时的位置
    		Lbar=CurrentBar;//记录交叉时的位置
    	}
    	
    	Bar_Highup=C[CurrentBar-Hbar];//回溯金叉时的价格
    	Bar_Lowdown=C[CurrentBar-Lbar];//回溯死叉时的价格
    	Highup=Highest(H[1],Max(Hbar-Lbar,X));//计算金叉时X周期内的最高价
    	Lowdown=Lowest(L[1],Max(Lbar-Hbar,X));//计算死叉时X周期内的最低价
    	
    	//核心计算公式，保存最新和历史的MACD值以及价格高低
    	If(Cross_up[1])
    	{
    		RD_MACD=D_MACD; //保存上一次MACD值
    		RHD=Bar_HD; //保存上一次交叉的价格
    		Bar_HD=Bar_Highup; //赋值本次交叉时的价格
    		D_MACD=AvgMACD; //赋值本次交叉时的DIFF值
    		HD=Highup;//赋值本次交叉时的X周期内的高低价
    	
    	}
    	If(Cross_dn[1])
    	{
    		RK_MACD=K_MACD; //保存上一次MACD值
    		RLD=Bar_LD; //保存上一次交叉的价格
    		Bar_LD=Bar_Lowdown;//赋值本次交叉时的价格
    		K_MACD=AvgMACD;//赋值本次交叉时的DIFF值
    		
    		LD=Lowdown;//赋值本次交叉时的X周期内的高低价
    	
    	}
    	
    	If(Cross_up[2])
    	{
    	
    		K=1;//打开开仓开关
    		condtion_D=Bar_HD<RHD and D_MACD>RD_MACD AND AvgMACD<0 ; //底背离计算公式
    	
    	}
    	If(Cross_dn[2])
    	{
    		
    		K=-1;//打开开仓开关
    		condtion_K=Bar_LD>RLD and K_MACD<RK_MACD AND AvgMACD>0; //顶背离计算公式
    	}
    
    //正常开仓条件	
    	if(K>0 and H>=HD  and HD>0 and MarketPosition==0)
    	{
    	
    		Buy(10,Max(open,HD));
    		LowAfterEntry = EntryPrice;//保存多头开仓价格;
    	
    	}
    	//if(K<0 and L<=LD  and LD>0 and MarketPosition==0)
    	//{
    	
    		//SellShort(0,Min(open,LD));
    		//HighAfterEntry = EntryPrice;//保存空头开仓价格;
    	//}
    //背离开仓条件
    	if(condtion_D and MarketPosition==0)
    	{
    		Buy(10,open);
    		LowAfterEntry = EntryPrice;//保存多头开仓价格;
    	
    	}
    	//If(condtion_K and MarketPosition==0)
    	//{
    		//SellShort(0,open);
    		//HighAfterEntry = EntryPrice;//保存空头开仓价格;
    		
    	//}
    		
    	If(MarketPosition == 0)   // 自适应参数默认值；
    	{
    		liQKA = 1;
    	}Else					 //当有持仓的情况下，liQKA会随着持仓时间的增加而逐渐减小，即止损止盈幅度乘数的减少。
    	{
    		liQKA = liQKA - 0.1; 
    		liQKA = Max(liQKA,0.3);
    	}
    	if(MarketPosition>0)
    	{
    	DliqPoint = LowAfterEntry - (Open*TRS/1000)*liQKA; //经过计算，这根吊灯出场线会随着持仓时间的增加变的越来越敏感；
    	}
    	if(MarketPosition<0)
    	{
    	KliqPoint = HighAfterEntry + (Open*TRS/1000)*liQKA; //经过计算，这根吊灯出场线会随着持仓时间的增加变的越来越敏感；
    	}
    	// 画线
    	Commentary("(Open*TRS/1000)*liQKA"+text((Open*TRS/1000)*liQKA));
    	//PlotNumeric("DliqPoint[1]",DliqPoint[1]);
    	//PlotNumeric("KliqPoint[1]",KliqPoint[1]);
    	
    	// 持有多单时
     	If(MarketPosition >0 And BarsSinceEntry >0  And Low <= DliqPoint[1] and DliqPoint[1]>0 ) 
    	{
    		
    			Sell(0,Min(Open,DliqPoint[1]));
    			K=0;
    	}
    		// 持有空单时
    	If(MarketPosition <0 And BarsSinceEntry >0  And High >= KliqPoint[1] and KliqPoint[1]>0 )
    	{		
    			BuyToCover(0,Max(Open,KliqPoint[1]));
    			K=0;
    	}	
    	
    	PlotNumeric("HD ",HD );
    	PlotNumeric("LD ",LD );
    	Commentary("K = "+Text(K));
    }
//------------------------------------------------------------------------
// 编译版本	2022/05/17 171201
// 版权所有	songshu123
// 更改声明	TradeBlazer Software保留对TradeBlazer平台
//			每一版本的TradeBlazer公式修改和重写的权利
//------------------------------------------------------------------------