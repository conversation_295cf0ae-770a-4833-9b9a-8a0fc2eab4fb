#coding:gbk
"""
K线合成模块
从QMT止盈止损下单模块中提取的K线合成功能

主要功能：
1. K线数据合成（多根K线合成为一根）
2. 成交量数据处理（累积转增量）
3. 滑动窗口数据管理
4. K线数据验证和清理

使用方法：
在QMT中导入此模块，调用相应函数进行K线合成处理
"""

import numpy as np
import pandas as pd
import datetime
import time
from typing import Dict, List, Tuple, Optional

# 从配置模块导入（如果需要的话，可以在这里定义默认配置）
STRATEGY_CONFIG = {
    'enable_kline_merge': True,
    'merge_ratio': 2,  # 2根K线合成1根
    'convert_cumulative_volume': True,
    'max_history_bars': 1000,
    'max_buffer_size': 2000,
    'volume_anomaly_threshold': 10.0,  # 成交量异常检测阈值
    'volume_history_size': 50,  # 成交量历史记录大小
}

# ============================================================================
# K线合成核心函数
# ============================================================================

def merge_two_bars(bar1, bar2):
    """
    合并两根K线为一根K线
    
    Args:
        bar1: 第一根K线数据 {'open': float, 'high': float, 'low': float, 'close': float, 'volume': float}
        bar2: 第二根K线数据
        
    Returns:
        dict: 合成后的K线数据
    """
    try:
        merged_bar = {
            'open': bar1['open'],  # 使用第一根K线的开盘价
            'high': max(bar1['high'], bar2['high']),  # 最高价取两根K线的最高值
            'low': min(bar1['low'], bar2['low']),  # 最低价取两根K线的最低值
            'close': bar2['close'],  # 使用第二根K线的收盘价
            'volume': bar1['volume'] + bar2['volume'],  # 成交量相加
            'start_time': bar1.get('time', ''),  # 开始时间
            'end_time': bar2.get('time', ''),  # 结束时间
        }
        
        return merged_bar
        
    except Exception as e:
        print(f"❌ 两根K线合成失败: {e}")
        return None

def merge_three_bars(bar1, bar2, bar3):
    """
    合并三根K线为一根K线
    
    Args:
        bar1, bar2, bar3: 三根K线数据
        
    Returns:
        dict: 合成后的K线数据
    """
    try:
        merged_bar = {
            'open': bar1['open'],  # 使用第一根K线的开盘价
            'high': max(bar1['high'], bar2['high'], bar3['high']),  # 最高价
            'low': min(bar1['low'], bar2['low'], bar3['low']),  # 最低价
            'close': bar3['close'],  # 使用最后一根K线的收盘价
            'volume': bar1['volume'] + bar2['volume'] + bar3['volume'],  # 成交量相加
            'start_time': bar1.get('time', ''),
            'end_time': bar3.get('time', ''),
        }
        
        return merged_bar
        
    except Exception as e:
        print(f"❌ 三根K线合成失败: {e}")
        return None

def merge_multiple_bars(bars_list):
    """
    合并多根K线为一根K线（通用函数）
    
    Args:
        bars_list: K线数据列表
        
    Returns:
        dict: 合成后的K线数据
    """
    try:
        if not bars_list or len(bars_list) == 0:
            return None
            
        if len(bars_list) == 1:
            return bars_list[0]
            
        # 计算合成K线的OHLCV
        merged_bar = {
            'open': bars_list[0]['open'],  # 第一根K线的开盘价
            'high': max(bar['high'] for bar in bars_list),  # 所有K线的最高价
            'low': min(bar['low'] for bar in bars_list),  # 所有K线的最低价
            'close': bars_list[-1]['close'],  # 最后一根K线的收盘价
            'volume': sum(bar['volume'] for bar in bars_list),  # 成交量相加
            'start_time': bars_list[0].get('time', ''),
            'end_time': bars_list[-1].get('time', ''),
            'merged_count': len(bars_list),  # 记录合成的K线数量
        }
        
        return merged_bar
        
    except Exception as e:
        print(f"❌ 多根K线合成失败: {e}")
        return None

# ============================================================================
# 成交量处理函数
# ============================================================================

def convert_cumulative_to_incremental_volume(volume_data, stock_code=""):
    """
    将累积成交量转换为增量成交量 - 参考6sk线文件的高级处理逻辑
    
    Args:
        volume_data: 成交量数据列表（累积成交量）
        stock_code: 股票代码（用于日志）
        
    Returns:
        list: 增量成交量数据列表
    """
    try:
        if not volume_data or len(volume_data) == 0:
            return []
            
        incremental_volumes = []
        
        # 第一个数据点的增量成交量就是其本身
        incremental_volumes.append(volume_data[0])
        
        # 计算后续数据点的增量成交量
        for i in range(1, len(volume_data)):
            current_cumulative = volume_data[i]
            previous_cumulative = volume_data[i-1]
            
            # 计算增量
            increment = current_cumulative - previous_cumulative
            
            # 异常检测和修正
            if increment < 0:
                # 成交量不应该减少，可能是数据重置或异常
                print(f"⚠️ 检测到成交量异常: {stock_code} 位置{i}, 当前={current_cumulative}, 前值={previous_cumulative}")
                # 使用当前累积值作为增量（假设是数据重置）
                increment = current_cumulative
                
            elif increment == 0:
                # 成交量没有变化，保持为0
                increment = 0
                
            elif increment > previous_cumulative * STRATEGY_CONFIG.get('volume_anomaly_threshold', 10.0):
                # 成交量异常增长，可能是数据错误
                print(f"⚠️ 检测到成交量异常增长: {stock_code} 位置{i}, 增量={increment}, 前值={previous_cumulative}")
                # 使用前一个增量值的平均值作为估算
                if len(incremental_volumes) > 0:
                    avg_increment = sum(incremental_volumes[-min(5, len(incremental_volumes)):]) / min(5, len(incremental_volumes))
                    increment = max(avg_increment, increment / 10)  # 取平均值或缩小10倍
                else:
                    increment = current_cumulative / 10  # 保守估算
                    
            incremental_volumes.append(increment)
            
        print(f"📊 成交量转换完成: {stock_code} {len(volume_data)}个数据点")
        return incremental_volumes
        
    except Exception as e:
        print(f"❌ 成交量转换失败: {e}")
        return volume_data  # 返回原始数据

def process_volume_data_qmt(ContextInfo, current_volume, stock_code=""):
    """
    处理QMT成交量数据 - 智能检测累积/增量模式
    
    Args:
        ContextInfo: QMT上下文对象
        current_volume: 当前成交量
        stock_code: 股票代码
        
    Returns:
        float: 处理后的增量成交量
    """
    try:
        # 初始化成交量历史记录
        if not hasattr(ContextInfo, 'volume_history'):
            ContextInfo.volume_history = {}
            
        if stock_code not in ContextInfo.volume_history:
            ContextInfo.volume_history[stock_code] = {
                'previous_volume': 0,
                'history': [],
                'is_cumulative': None,  # None=未确定, True=累积, False=增量
                'detection_count': 0
            }
            
        vol_info = ContextInfo.volume_history[stock_code]
        
        # 智能检测成交量模式（前10个数据点用于检测）
        if vol_info['detection_count'] < 10:
            vol_info['detection_count'] += 1
            vol_info['history'].append(current_volume)
            
            # 检测逻辑：如果成交量持续增长且增长幅度较大，可能是累积模式
            if len(vol_info['history']) >= 3:
                recent_volumes = vol_info['history'][-3:]
                is_increasing = all(recent_volumes[i] <= recent_volumes[i+1] for i in range(len(recent_volumes)-1))
                growth_ratio = recent_volumes[-1] / recent_volumes[0] if recent_volumes[0] > 0 else 1
                
                if is_increasing and growth_ratio > 2.0:
                    vol_info['is_cumulative'] = True
                    print(f"📊 检测到累积成交量模式: {stock_code}")
                elif vol_info['detection_count'] >= 5:
                    vol_info['is_cumulative'] = False
                    print(f"📊 检测到增量成交量模式: {stock_code}")
                    
        # 根据检测结果处理成交量
        if vol_info['is_cumulative'] is True:
            # 累积模式：计算增量
            if vol_info['previous_volume'] > 0:
                increment = current_volume - vol_info['previous_volume']
                if increment < 0:
                    # 可能是新的交易日，重置
                    increment = current_volume
                    print(f"📊 成交量重置检测: {stock_code}")
            else:
                increment = current_volume
                
            vol_info['previous_volume'] = current_volume
            return max(0, increment)
            
        elif vol_info['is_cumulative'] is False:
            # 增量模式：直接使用
            return current_volume
            
        else:
            # 未确定模式：保守处理，直接使用当前值
            return current_volume
            
    except Exception as e:
        print(f"❌ 成交量处理异常: {e}")
        return current_volume

# ============================================================================
# K线合成主流程函数
# ============================================================================

def process_kline_merge(ContextInfo, new_kline_data):
    """
    处理K线合成主流程
    
    Args:
        ContextInfo: QMT上下文对象
        new_kline_data: 新的K线数据
    """
    try:
        # 检查是否启用K线合成
        if not STRATEGY_CONFIG.get('enable_kline_merge', True):
            # 不合成，直接添加到缓冲区
            if not hasattr(ContextInfo, 'merged_klines'):
                ContextInfo.merged_klines = []
            ContextInfo.merged_klines.append(new_kline_data)
            return
            
        # 初始化合成缓冲区
        if not hasattr(ContextInfo, 'kline_merge_buffer'):
            ContextInfo.kline_merge_buffer = []
            
        if not hasattr(ContextInfo, 'merged_klines'):
            ContextInfo.merged_klines = []
            
        # 处理成交量（如果启用转换）
        if STRATEGY_CONFIG.get('convert_cumulative_volume', True):
            processed_volume = process_volume_data_qmt(ContextInfo, new_kline_data['volume'], 
                                                     getattr(ContextInfo, 'stock', ''))
            new_kline_data['volume'] = processed_volume
            
        # 添加到合成缓冲区
        ContextInfo.kline_merge_buffer.append(new_kline_data)
        
        # 检查是否达到合成条件
        merge_ratio = STRATEGY_CONFIG.get('merge_ratio', 2)
        if len(ContextInfo.kline_merge_buffer) >= merge_ratio:
            # 执行合成
            bars_to_merge = ContextInfo.kline_merge_buffer[:merge_ratio]
            merged_bar = merge_multiple_bars(bars_to_merge)
            
            if merged_bar:
                # 添加到合成K线列表
                ContextInfo.merged_klines.append(merged_bar)
                
                print(f"📊 K线合成完成: {merge_ratio}根→1根, OHLCV=[{merged_bar['open']:.3f}, {merged_bar['high']:.3f}, {merged_bar['low']:.3f}, {merged_bar['close']:.3f}, {merged_bar['volume']:.0f}]")
                
                # 移除已合成的K线
                ContextInfo.kline_merge_buffer = ContextInfo.kline_merge_buffer[merge_ratio:]
                
                # 维护滑动窗口
                maintain_sliding_window(ContextInfo)
                
    except Exception as e:
        print(f"❌ K线合成处理异常: {e}")

def maintain_sliding_window(ContextInfo):
    """
    维护滑动窗口数据管理
    
    Args:
        ContextInfo: QMT上下文对象
    """
    try:
        if not hasattr(ContextInfo, 'merged_klines'):
            return
            
        # 计算最大K线数量
        MAX_KLINES = STRATEGY_CONFIG['max_history_bars']
        current_count = len(ContextInfo.merged_klines)
        
        if current_count > MAX_KLINES:
            # FIFO：移除最旧的数据，保留最新的数据
            removed_count = current_count - MAX_KLINES
            ContextInfo.merged_klines = ContextInfo.merged_klines[-MAX_KLINES:]
            
            print(f"🔄 滑动窗口清理: 移除{removed_count}个旧K线，保留{len(ContextInfo.merged_klines)}个")
            
            # 更新滑动窗口统计
            if not hasattr(ContextInfo, 'sliding_window_stats'):
                ContextInfo.sliding_window_stats = {'total_removed': 0, 'cleanup_count': 0}
                
            ContextInfo.sliding_window_stats['total_removed'] += removed_count
            ContextInfo.sliding_window_stats['cleanup_count'] += 1
            
            # 每10次清理输出一次统计
            if ContextInfo.sliding_window_stats['cleanup_count'] % 10 == 0:
                print(f"📊 滑动窗口统计: 已清理{ContextInfo.sliding_window_stats['cleanup_count']}次，"
                      f"累计移除{ContextInfo.sliding_window_stats['total_removed']}个K线")
                      
        # 内存优化：定期强制垃圾回收
        if hasattr(ContextInfo, 'sliding_window_stats') and ContextInfo.sliding_window_stats['cleanup_count'] % 50 == 0:
            import gc
            gc.collect()
            print("🧹 执行内存垃圾回收")
            
    except Exception as e:
        print(f"❌ 滑动窗口维护失败: {e}")

def get_merged_kline_data(ContextInfo):
    """
    获取合成K线数据用于技术指标计算
    
    Args:
        ContextInfo: QMT上下文对象
        
    Returns:
        dict: 包含OHLCV数组的字典
    """
    try:
        if not hasattr(ContextInfo, 'merged_klines') or not ContextInfo.merged_klines:
            return None
            
        merged_klines = ContextInfo.merged_klines
        
        return {
            'opens': [kline['open'] for kline in merged_klines],
            'highs': [kline['high'] for kline in merged_klines],
            'lows': [kline['low'] for kline in merged_klines],
            'closes': [kline['close'] for kline in merged_klines],
            'volumes': [kline['volume'] for kline in merged_klines],
            'count': len(merged_klines)
        }
        
    except Exception as e:
        print(f"❌ 获取合成K线数据失败: {e}")
        return None

# ============================================================================
# K线数据验证和清理函数
# ============================================================================

def validate_kline_data(kline_data):
    """
    验证K线数据的有效性
    
    Args:
        kline_data: K线数据字典
        
    Returns:
        bool: 数据是否有效
    """
    try:
        required_fields = ['open', 'high', 'low', 'close', 'volume']
        
        # 检查必需字段
        for field in required_fields:
            if field not in kline_data:
                print(f"❌ K线数据缺少字段: {field}")
                return False
                
        # 检查价格数据的逻辑性
        open_price = kline_data['open']
        high_price = kline_data['high']
        low_price = kline_data['low']
        close_price = kline_data['close']
        volume = kline_data['volume']
        
        # 价格必须为正数
        if any(price <= 0 for price in [open_price, high_price, low_price, close_price]):
            print(f"❌ K线数据包含非正价格")
            return False
            
        # 最高价必须 >= 最低价
        if high_price < low_price:
            print(f"❌ K线数据逻辑错误: 最高价({high_price}) < 最低价({low_price})")
            return False
            
        # 开盘价和收盘价必须在最高价和最低价之间
        if not (low_price <= open_price <= high_price):
            print(f"❌ K线数据逻辑错误: 开盘价({open_price})不在合理范围[{low_price}, {high_price}]")
            return False
            
        if not (low_price <= close_price <= high_price):
            print(f"❌ K线数据逻辑错误: 收盘价({close_price})不在合理范围[{low_price}, {high_price}]")
            return False
            
        # 成交量必须非负
        if volume < 0:
            print(f"❌ K线数据包含负成交量: {volume}")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ K线数据验证异常: {e}")
        return False

def clean_kline_buffer(ContextInfo):
    """
    清理K线缓冲区中的异常数据
    
    Args:
        ContextInfo: QMT上下文对象
    """
    try:
        # 缓冲区异常清理
        max_buffer_size = STRATEGY_CONFIG['max_buffer_size']
        if hasattr(ContextInfo, 'kline_buffer') and len(ContextInfo.kline_buffer) > max_buffer_size:
            ContextInfo.kline_buffer = ContextInfo.kline_buffer[-1:]
            print("⚠️ 缓冲区异常增长，已清理")
            
        # 合成K线缓冲区清理
        if hasattr(ContextInfo, 'merged_klines'):
            # 验证并移除无效数据
            valid_klines = []
            removed_count = 0
            
            for kline in ContextInfo.merged_klines:
                if validate_kline_data(kline):
                    valid_klines.append(kline)
                else:
                    removed_count += 1
                    
            if removed_count > 0:
                ContextInfo.merged_klines = valid_klines
                print(f"🧹 清理无效K线数据: 移除{removed_count}个，保留{len(valid_klines)}个")
                
        # 滑动窗口数据管理
        maintain_sliding_window(ContextInfo)
        
    except Exception as e:
        print(f"❌ K线缓冲区清理异常: {e}")

# ============================================================================
# 工具函数
# ============================================================================

def format_kline_data(open_price, high_price, low_price, close_price, volume, timestamp=None):
    """
    格式化K线数据为标准字典格式
    
    Args:
        open_price, high_price, low_price, close_price: OHLC价格
        volume: 成交量
        timestamp: 时间戳
        
    Returns:
        dict: 标准格式的K线数据
    """
    return {
        'open': float(open_price),
        'high': float(high_price),
        'low': float(low_price),
        'close': float(close_price),
        'volume': float(volume),
        'time': timestamp or datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    }

def get_kline_statistics(klines_list):
    """
    获取K线数据统计信息
    
    Args:
        klines_list: K线数据列表
        
    Returns:
        dict: 统计信息
    """
    try:
        if not klines_list:
            return {}
            
        closes = [kline['close'] for kline in klines_list]
        volumes = [kline['volume'] for kline in klines_list]
        
        return {
            'count': len(klines_list),
            'price_range': {
                'min': min(closes),
                'max': max(closes),
                'current': closes[-1] if closes else 0
            },
            'volume_stats': {
                'total': sum(volumes),
                'average': sum(volumes) / len(volumes) if volumes else 0,
                'max': max(volumes) if volumes else 0
            }
        }
        
    except Exception as e:
        print(f"❌ K线统计计算异常: {e}")
        return {}

# ============================================================================
# 模块信息
# ============================================================================

__version__ = "1.0.0"
__author__ = "K线合成模块"
__description__ = """
K线合成模块

主要功能：
1. K线数据合成（多根K线合成为一根）
2. 成交量数据处理（累积转增量）
3. 滑动窗口数据管理
4. K线数据验证和清理

核心函数：
- merge_two_bars, merge_three_bars, merge_multiple_bars: K线合成函数
- convert_cumulative_to_incremental_volume: 成交量转换
- process_kline_merge: K线合成主流程
- maintain_sliding_window: 滑动窗口管理
- get_merged_kline_data: 获取合成K线数据

使用方法：
在QMT中导入此模块，调用相应函数进行K线合成处理
"""

if __name__ == "__main__":
    print(__description__)
    print(f"版本: {__version__}")
    print("✅ K线合成模块加载完成")
