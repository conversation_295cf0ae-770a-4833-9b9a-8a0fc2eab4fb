#coding:gbk
"""
QMT止盈止损主模块
整合历史数据下载、K线合成、止盈止损、下单执行四个模块的主控制模块

主要功能：
1. 模块整合和初始化
2. 策略主流程控制
3. QMT标准框架函数（init、handlebar）
4. 配置管理
5. 状态管理

使用方法：
在QMT中导入此模块，作为策略的主入口
"""

import datetime
import time
import sys
import os

# 导入子模块
try:
    from 历史数据下载和预处理模块 import (
        MarketDataBuffer, init_strategy_download_only, 
        update_buffer_with_current_data_qmt, get_safe_market_data_for_trading
    )
    from K线合成模块 import (
        process_kline_merge, get_merged_kline_data, 
        maintain_sliding_window, clean_kline_buffer
    )
    from 止盈止损模块 import (
        check_buy_signal_three_bars, check_exit_conditions_donchian,
        check_time_risk_control, update_strategy_state, get_strategy_state
    )
    from 下单执行模块 import (
        execute_buy_order, execute_sell_order, get_current_position_info,
        manage_pending_orders, force_close_all_positions, get_main_account_id
    )
    print("✅ 所有子模块导入成功")
except ImportError as e:
    print(f"❌ 子模块导入失败: {e}")
    print("💡 请确保所有子模块文件都在同一目录下")

# ============================================================================
# 策略配置
# ============================================================================

STRATEGY_CONFIG = {
    # 数据缓冲区配置
    'enable_data_buffer': True,         # 启用数据缓冲区功能
    'buffer_size': 100,                 # 缓冲区大小
    'history_data_count': 60,           # 历史数据数量
    'min_trading_periods': 20,          # 最少交易周期
    
    # K线合成配置
    'enable_kline_merge': True,         # 启用K线合成
    'merge_ratio': 2,                   # 合成比例（2根合成1根）
    'convert_cumulative_volume': True,  # 转换累积成交量
    'max_history_bars': 1000,           # 最大历史K线数
    'max_buffer_size': 2000,            # 最大缓冲区大小
    
    # 止盈止损配置
    'take_profit_ratio': 0.03,          # 3%止盈
    'stop_loss_ratio': 0.02,            # 2%止损
    'atr_period': 14,                   # ATR计算周期
    'atr_stop_loss_multiplier': 2.0,    # ATR止损倍数
    'atr_trigger_multiplier': 3.0,      # ATR移动止盈触发倍数
    'donchian_period': 20,              # 唐奇安通道周期
    
    # 买入信号配置
    'buy_signal_count_required': 3,     # 买入信号需要的数据次数
    'buy_signal_enabled': True,         # 启用买入信号检查
    
    # 下单配置
    'buy_hang_offset_ratio': 0.002,     # 买入挂单偏移比例
    'sell_hang_offset_ratio': 0.002,    # 卖出挂单偏移比例
    'default_buy_volume': 100,          # 默认买入数量
    'min_trade_unit': 10,               # 最小交易单位
    
    # 时间风控配置
    'enable_time_risk_control': True,   # 启用时间风控
    'no_open_after_time': '14:55:00',   # 此时间后不开仓
    'force_close_time': '14:56:00',     # 强制平仓时间
    
    # 调试配置
    'debug_mode': False,                # 调试模式
    'use_main_stock': True,             # 使用主图股票
}

# ============================================================================
# QMT标准框架函数
# ============================================================================

def init(C):
    """
    QMT策略初始化函数 - 集成数据缓冲区功能
    """
    print("🚀 QMT止盈止损策略初始化 - 模块化版本")
    print("="*60)
    
    # 基础设置
    C.stock = C.stockcode + '.' + C.market
    print(f"📊 交易标的: {C.stock}")
    print(f"🕐 初始化时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 账户信息设置
    try:
        C.acct = get_main_account_id(C)
        C.acct_type = globals().get('accountType', 'STOCK')
        
        if hasattr(C, 'set_account'):
            C.set_account(C.acct)
            print(f"✅ 账户设置成功: {C.acct}")
        else:
            print(f"📊 账户ID: {C.acct} (无set_account方法)")
            
    except Exception as e:
        print(f"⚠️ 账户设置异常: {e}")
        C.acct = "*********"
        C.acct_type = "STOCK"
        
    # 数据缓冲区功能初始化
    if STRATEGY_CONFIG.get('enable_data_buffer', False):
        print(f"\n📥 数据缓冲区功能已启用，开始初始化...")
        
        try:
            # 创建数据缓冲区
            buffer_size = STRATEGY_CONFIG.get('buffer_size', 100)
            C.data_buffer = MarketDataBuffer(buffer_size)
            print(f"📊 数据缓冲区初始化完成，容量: {buffer_size}根K线")
            
            # 预加载历史数据
            print("📥 获取历史数据填充缓冲区...")
            history_count = STRATEGY_CONFIG.get('history_data_count', 60)
            
            success = C.data_buffer.preload_history_data(
                C, C.stock, 'tick', history_count
            )
            
            if success:
                print(f"✅ 历史数据预加载成功，缓冲区包含 {C.data_buffer.get_data_count()} 根K线")
            else:
                print("⚠️ 历史数据预加载失败，将使用实时数据逐步填充")
                
            C.buffer_initialized = True
            
        except Exception as e:
            print(f"⚠️ 数据缓冲区初始化异常: {e}")
            import traceback
            traceback.print_exc()
            C.buffer_initialized = True
    else:
        print(f"\n📊 数据缓冲区功能已禁用，使用传统模式")
        
    # 初始化策略状态
    C.strategy_state = {}
    C.data_count = 0
    C.buy_signal_triggered = False
    
    if not hasattr(C, 'buffer_initialized'):
        C.buffer_initialized = False
    C.debug_mode = STRATEGY_CONFIG.get('debug_mode', False)
    
    print(f"\n💡 策略初始化完成，等待handlebar进行数据获取...")
    print("="*60)

def handlebar(C):
    """
    QMT策略主函数 - 集成所有模块的完整交易逻辑
    """
    try:
        # 安全检查 - 只在最新K线执行逻辑
        if not C.is_last_bar():
            return
            
        # 数据缓冲区更新
        if STRATEGY_CONFIG.get('enable_data_buffer', False):
            if not getattr(C, 'buffer_initialized', False):
                print("⚠️ 数据缓冲区未初始化，跳过本次执行")
                return
                
            # 更新数据缓冲区
            update_buffer_with_current_data_qmt(C)
            
            # 检查数据是否足够
            if hasattr(C, 'data_buffer'):
                min_periods = STRATEGY_CONFIG.get('min_trading_periods', 20)
                if not C.data_buffer.is_ready_for_trading(min_periods):
                    if getattr(C, 'debug_mode', False):
                        print(f"⏳ 数据不足，需要{min_periods}根K线，当前{C.data_buffer.get_data_count()}根")
                    return
                    
        # 获取当前价格和成交量
        current_price = None
        volume = 0
        
        if STRATEGY_CONFIG.get('enable_data_buffer', False) and hasattr(C, 'data_buffer'):
            # 使用缓冲区数据
            current_price = C.data_buffer.get_current_price()
            volume = C.data_buffer.get_current_volume()
            
            if current_price is None or current_price <= 0:
                print("⚠️ 缓冲区无有效价格数据，跳过本次执行")
                return
        else:
            # 使用传统方法获取市场数据
            market_data = get_safe_market_data_for_trading(C)
            if not market_data:
                print("⚠️ 无法获取市场数据，跳过本次执行")
                return
            current_price, volume = market_data
            
        stock_code = C.stock
        print(f"\n📊 策略执行 - {stock_code}")
        print(f"📊 当前价格: {current_price:.3f}, 成交量: {volume}")
        
        # 时间风控检查
        time_control = check_time_risk_control(C)
        
        # 强制平仓检查
        if time_control['force_close']:
            print(f"🚨 时间风控触发强制平仓: {time_control['reason']}")
            force_close_result = force_close_all_positions(C, "时间风控强制平仓")
            print(f"📊 强制平仓结果: {force_close_result['message']}")
            return
            
        # 管理未成交的委托单
        manage_result = manage_pending_orders(C, stock_code, current_price)
        if manage_result['cancelled'] > 0:
            print(f"🔄 委托管理: 撤销{manage_result['cancelled']}个，重新挂单{manage_result['reordered']}个")
            
        # 更新策略状态
        update_strategy_state(C, stock_code, current_price)
        
        # 获取持仓信息和策略状态
        position = get_current_position_info(C, stock_code)
        strategy_state = get_strategy_state(C, stock_code)
        
        # 构建市场数据用于技术指标计算
        market_data_dict = None
        if STRATEGY_CONFIG.get('enable_data_buffer', False) and hasattr(C, 'data_buffer'):
            # 使用缓冲区数据构建市场数据
            if C.data_buffer.get_data_count() > 0:
                market_data_dict = {
                    'highs': list(C.data_buffer.high_buffer),
                    'lows': list(C.data_buffer.low_buffer),
                    'closes': list(C.data_buffer.close_buffer),
                    'opens': list(C.data_buffer.open_buffer),
                    'volumes': list(C.data_buffer.volume_buffer)
                }
        else:
            # 使用简化的市场数据
            market_data_dict = {
                'highs': [current_price] * 20,
                'lows': [current_price] * 20,
                'closes': [current_price] * 20
            }
            
        if not position['has_position']:
            # 无持仓 - 检查买入信号
            print("📈 无持仓状态，检查买入信号")
            
            buy_signal_result = check_buy_signal_three_bars(C, stock_code, market_data_dict)
            print(f"📊 买入信号检查: {buy_signal_result['reason']}")
            
            if 'data_count' in buy_signal_result:
                print(f"   数据计数: {buy_signal_result['data_count']}")
                
            if buy_signal_result['buy_signal']:
                print("🎯 买入信号触发，执行买入")
                buy_result = execute_buy_order(C, stock_code, current_price)
                if buy_result['success']:
                    print(f"✅ 买入订单提交成功: {buy_result['volume']}股@{buy_result['price']:.3f}")
                    print(f"   订单ID: {buy_result['order_id']}")
                else:
                    print(f"❌ 买入订单失败: {buy_result['reason']}")
            else:
                print("⏳ 买入信号未触发，继续等待")
                
        else:
            # 有持仓 - 检查止盈止损
            print(f"📊 持仓状态: {position['volume']}股@{position['avg_price']:.3f}")
            print(f"   当前市值: {position.get('market_value', 0):.2f}")
            print(f"   浮动盈亏: {position.get('position_profit', 0):.2f}")
            
            # 使用策略状态中的数据进行止盈止损检查
            entry_price = strategy_state['entry_price'] or position['avg_price']
            highest_price = strategy_state['highest_price_since_entry'] or current_price
            trailing_stop = strategy_state['trailing_stop_price']
            
            print(f"📊 策略状态: 入场价={entry_price:.3f}, 最高价={highest_price:.3f}, 移动止盈={trailing_stop:.3f}")
            
            # 执行止盈止损检查
            exit_result = check_exit_conditions_donchian(
                C, current_price, entry_price, highest_price, trailing_stop, market_data_dict
            )
            
            print(f"🔍 止盈止损检查: {exit_result['reason']}")
            
            if exit_result['should_exit']:
                print(f"🎯 平仓信号触发: {exit_result['reason']}")
                
                # 执行卖出
                sell_result = execute_sell_order(C, stock_code, current_price, reason=exit_result['reason'])
                if sell_result['success']:
                    print(f"✅ 卖出订单提交成功: {sell_result['volume']}股@{sell_result['price']:.3f}")
                    print(f"   订单ID: {sell_result['order_id']}")
                    print(f"   卖出原因: {sell_result['reason']}")
                else:
                    print(f"❌ 卖出订单失败: {sell_result['reason']}")
                    
                # 更新策略状态
                if 'new_trailing_stop' in exit_result:
                    C.strategy_states[stock_code]['trailing_stop_price'] = exit_result['new_trailing_stop']
                    
            else:
                print(f"📈 继续持仓: {exit_result['reason']}")
                
                # 更新策略状态
                if 'new_trailing_stop' in exit_result:
                    C.strategy_states[stock_code]['trailing_stop_price'] = exit_result['new_trailing_stop']
                if 'new_highest_price' in exit_result:
                    C.strategy_states[stock_code]['highest_price_since_entry'] = exit_result['new_highest_price']
                    
        # 打印策略状态摘要
        print_strategy_status(C, stock_code, current_price,
                            strategy_state['entry_price'],
                            strategy_state['highest_price_since_entry'],
                            strategy_state['trailing_stop_price'])
                            
    except Exception as e:
        print(f"❌ 策略执行异常: {e}")
        import traceback
        traceback.print_exc()

# ============================================================================
# 辅助函数
# ============================================================================

def print_strategy_status(ContextInfo, stock_code, current_price, entry_price=0, highest_price=0, trailing_stop=0):
    """
    打印策略状态信息
    """
    print(f"\n📊 策略状态 - {stock_code}")
    print("=" * 50)
    
    # 持仓信息
    position = get_current_position_info(ContextInfo, stock_code)
    print(f"💼 持仓状态: {'有持仓' if position['has_position'] else '空仓'}")
    if position['has_position']:
        print(f"   持仓数量: {position['volume']}股")
        print(f"   持仓成本: {position['avg_price']:.3f}")
        if 'market_value' in position:
            print(f"   市值: {position['market_value']:.2f}")
            
    # 价格信息
    print(f"💰 价格信息:")
    print(f"   当前价格: {current_price:.3f}")
    if entry_price > 0:
        profit_pct = (current_price - entry_price) / entry_price * 100
        print(f"   入场价格: {entry_price:.3f}")
        print(f"   盈亏比例: {profit_pct:+.2f}%")
    if highest_price > 0:
        print(f"   最高价格: {highest_price:.3f}")
    if trailing_stop > 0:
        print(f"   移动止盈: {trailing_stop:.3f}")

# ============================================================================
# 模块信息
# ============================================================================

__version__ = "1.0.0"
__author__ = "QMT止盈止损主模块"
__description__ = """
QMT止盈止损主模块 - 模块化版本

整合功能：
1. 历史数据下载和预处理
2. K线合成和数据管理
3. 止盈止损策略逻辑
4. 下单执行和委托管理

主要特点：
✅ 模块化设计，功能分离
✅ 完整的买入→持仓→卖出流程
✅ 智能委托单管理
✅ ATR动态止损
✅ 唐奇安通道移动止盈
✅ 时间风控管理
✅ 数据缓冲区支持

使用方法：
在QMT中导入此主模块，作为策略的入口点
"""

if __name__ == "__main__":
    print(__description__)
    print(f"版本: {__version__}")
    print("✅ QMT止盈止损主模块加载完成")
    print("\n📋 子模块状态:")
    print("   ✅ 历史数据下载和预处理模块")
    print("   ✅ K线合成模块")
    print("   ✅ 止盈止损模块")
    print("   ✅ 下单执行模块")
