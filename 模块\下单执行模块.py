#coding:gbk
"""
下单执行模块
从QMT止盈止损下单模块中提取的下单执行功能

主要功能：
1. 买入订单执行
2. 卖出订单执行
3. 委托单查询和管理
4. 订单撤销功能
5. 持仓信息查询
6. 强制平仓功能

使用方法：
在QMT中导入此模块，调用相应函数进行订单执行和管理
"""

import numpy as np
import pandas as pd
import datetime
import time
from typing import Dict, List, Tuple, Optional

# 默认策略配置
STRATEGY_CONFIG = {
    'buy_hang_offset_ratio': 0.002,     # 买入挂单偏移比例（正值=低于市价）
    'sell_hang_offset_ratio': 0.002,    # 卖出挂单偏移比例（正值=高于市价）
    'default_buy_volume': 100,          # 默认买入数量（股）
    'min_trade_unit': 10,               # 最小交易单位（股）
    'max_order_retry': 3,               # 最大重试次数
    'order_timeout': 30,                # 订单超时时间（秒）
}

# ============================================================================
# 买入订单执行函数
# ============================================================================

def execute_buy_order(ContextInfo, stock_code, current_price, volume=None, reason="买入信号"):
    """
    执行买入订单 - 支持挂单偏移和10股交易单位
    
    Args:
        ContextInfo: QMT上下文对象
        stock_code: 股票代码
        current_price: 当前价格
        volume: 买入数量（股），如果为None则使用默认值
        reason: 买入原因
        
    Returns:
        dict: 执行结果 {'success': bool, 'order_id': str, 'price': float, 'volume': int, 'reason': str}
    """
    try:
        print(f"\n🎯 执行买入订单: {stock_code}")
        print(f"📊 当前价格: {current_price:.3f}")
        print(f"📝 买入原因: {reason}")
        
        # 时间风控检查
        from 止盈止损模块 import check_time_risk_control
        time_control = check_time_risk_control(ContextInfo)
        if not time_control['allow_open']:
            return {
                'success': False,
                'order_id': '',
                'price': 0,
                'volume': 0,
                'reason': f"时间风控禁止开仓: {time_control['reason']}"
            }
            
        # 获取账户ID
        account_id = get_main_account_id(ContextInfo)
        
        # 计算买入数量
        if volume is None:
            volume = STRATEGY_CONFIG.get('default_buy_volume', 100)
            
        # 确保买入数量是最小交易单位的整数倍
        min_unit = STRATEGY_CONFIG.get('min_trade_unit', 10)
        volume = (volume // min_unit) * min_unit
        
        if volume <= 0:
            return {
                'success': False,
                'order_id': '',
                'price': 0,
                'volume': 0,
                'reason': f"买入数量无效: {volume}股"
            }
            
        # 计算挂单价格（考虑偏移）
        offset_ratio = STRATEGY_CONFIG.get('buy_hang_offset_ratio', 0.002)
        if offset_ratio > 0:
            # 正偏移：低于市价买入（保守策略）
            buy_price = current_price * (1 - offset_ratio)
            price_desc = f"低于市价{offset_ratio*100:.2f}%"
        elif offset_ratio < 0:
            # 负偏移：高于市价买入（激进策略）
            buy_price = current_price * (1 + abs(offset_ratio))
            price_desc = f"高于市价{abs(offset_ratio)*100:.2f}%"
        else:
            # 无偏移：市价买入
            buy_price = current_price
            price_desc = "市价"
            
        # 价格精度处理（保留3位小数）
        buy_price = round(buy_price, 3)
        
        print(f"💰 买入参数: {volume}股@{buy_price:.3f} ({price_desc})")
        
        # 执行买入订单
        try:
            # 使用QMT标准下单函数
            order_result = passorder(
                23,  # 买入
                -1,
                account_id,
                stock_code,
                buy_price,
                volume,
                ContextInfo
            )
            
            # 检查下单结果
            if order_result and len(order_result) > 0:
                order_info = order_result[0]
                order_id = str(order_info.get('order_id', ''))
                
                if order_id and order_id != '0':
                    print(f"✅ 买入订单提交成功")
                    print(f"   订单ID: {order_id}")
                    print(f"   买入价格: {buy_price:.3f}")
                    print(f"   买入数量: {volume}股")
                    
                    return {
                        'success': True,
                        'order_id': order_id,
                        'price': buy_price,
                        'volume': volume,
                        'reason': reason
                    }
                else:
                    error_msg = order_info.get('error_msg', '未知错误')
                    print(f"❌ 买入订单失败: {error_msg}")
                    return {
                        'success': False,
                        'order_id': '',
                        'price': buy_price,
                        'volume': volume,
                        'reason': f"下单失败: {error_msg}"
                    }
            else:
                print(f"❌ 买入订单失败: 无返回结果")
                return {
                    'success': False,
                    'order_id': '',
                    'price': buy_price,
                    'volume': volume,
                    'reason': "下单失败: 无返回结果"
                }
                
        except Exception as e:
            print(f"❌ 买入订单执行异常: {e}")
            return {
                'success': False,
                'order_id': '',
                'price': buy_price,
                'volume': volume,
                'reason': f"下单异常: {e}"
            }
            
    except Exception as e:
        print(f"❌ 买入订单准备异常: {e}")
        return {
            'success': False,
            'order_id': '',
            'price': 0,
            'volume': 0,
            'reason': f"买入准备异常: {e}"
        }

def execute_sell_order(ContextInfo, stock_code, current_price, volume=None, reason="卖出信号", auto_cancel_pending=True):
    """
    执行卖出订单 - 支持挂单偏移和自动撤单
    
    Args:
        ContextInfo: QMT上下文对象
        stock_code: 股票代码
        current_price: 当前价格
        volume: 卖出数量（股），如果为None则卖出全部持仓
        reason: 卖出原因
        auto_cancel_pending: 是否自动撤销未成交的卖出委托
        
    Returns:
        dict: 执行结果
    """
    try:
        print(f"\n🎯 执行卖出订单: {stock_code}")
        print(f"📊 当前价格: {current_price:.3f}")
        print(f"📝 卖出原因: {reason}")
        
        # 获取账户ID
        account_id = get_main_account_id(ContextInfo)
        
        # 获取当前持仓
        position = get_current_position_info(ContextInfo, stock_code)
        if not position['has_position']:
            return {
                'success': False,
                'order_id': '',
                'price': 0,
                'volume': 0,
                'reason': "无持仓，无法卖出"
            }
            
        # 确定卖出数量
        if volume is None:
            volume = position['volume']
        else:
            volume = min(volume, position['volume'])
            
        # 确保卖出数量是最小交易单位的整数倍
        min_unit = STRATEGY_CONFIG.get('min_trade_unit', 10)
        volume = (volume // min_unit) * min_unit
        
        if volume <= 0:
            return {
                'success': False,
                'order_id': '',
                'price': 0,
                'volume': 0,
                'reason': f"卖出数量无效: {volume}股"
            }
            
        # 自动撤销未成交的卖出委托（如果启用）
        if auto_cancel_pending:
            pending_orders = query_orders(ContextInfo, stock_code, 'SELL')
            pending_sell_orders = [order for order in pending_orders if order['is_pending']]
            
            if pending_sell_orders:
                print(f"🔄 发现{len(pending_sell_orders)}个未成交卖出委托，先撤销...")
                cancel_ids = [order['order_id'] for order in pending_sell_orders]
                cancel_result = cancel_orders(ContextInfo, cancel_ids)
                print(f"📊 撤单结果: 成功{cancel_result['cancel_success']}个")
                
                # 等待撤单完成
                time.sleep(1)
                
        # 计算挂单价格（考虑偏移）
        offset_ratio = STRATEGY_CONFIG.get('sell_hang_offset_ratio', 0.002)
        if offset_ratio > 0:
            # 正偏移：高于市价卖出（保守策略）
            sell_price = current_price * (1 + offset_ratio)
            price_desc = f"高于市价{offset_ratio*100:.2f}%"
        elif offset_ratio < 0:
            # 负偏移：低于市价卖出（激进策略）
            sell_price = current_price * (1 - abs(offset_ratio))
            price_desc = f"低于市价{abs(offset_ratio)*100:.2f}%"
        else:
            # 无偏移：市价卖出
            sell_price = current_price
            price_desc = "市价"
            
        # 价格精度处理
        sell_price = round(sell_price, 3)
        
        print(f"💰 卖出参数: {volume}股@{sell_price:.3f} ({price_desc})")
        
        # 执行卖出订单
        try:
            order_result = passorder(
                24,  # 卖出
                -1,
                account_id,
                stock_code,
                sell_price,
                volume,
                ContextInfo
            )
            
            # 检查下单结果
            if order_result and len(order_result) > 0:
                order_info = order_result[0]
                order_id = str(order_info.get('order_id', ''))
                
                if order_id and order_id != '0':
                    print(f"✅ 卖出订单提交成功")
                    print(f"   订单ID: {order_id}")
                    print(f"   卖出价格: {sell_price:.3f}")
                    print(f"   卖出数量: {volume}股")
                    
                    return {
                        'success': True,
                        'order_id': order_id,
                        'price': sell_price,
                        'volume': volume,
                        'reason': reason
                    }
                else:
                    error_msg = order_info.get('error_msg', '未知错误')
                    print(f"❌ 卖出订单失败: {error_msg}")
                    return {
                        'success': False,
                        'order_id': '',
                        'price': sell_price,
                        'volume': volume,
                        'reason': f"下单失败: {error_msg}"
                    }
            else:
                print(f"❌ 卖出订单失败: 无返回结果")
                return {
                    'success': False,
                    'order_id': '',
                    'price': sell_price,
                    'volume': volume,
                    'reason': "下单失败: 无返回结果"
                }
                
        except Exception as e:
            print(f"❌ 卖出订单执行异常: {e}")
            return {
                'success': False,
                'order_id': '',
                'price': sell_price,
                'volume': volume,
                'reason': f"下单异常: {e}"
            }
            
    except Exception as e:
        print(f"❌ 卖出订单准备异常: {e}")
        return {
            'success': False,
            'order_id': '',
            'price': 0,
            'volume': 0,
            'reason': f"卖出准备异常: {e}"
        }

# ============================================================================
# 委托单查询和管理函数
# ============================================================================

def query_orders(ContextInfo, stock_code=None, order_type='ALL'):
    """
    查询委托单
    
    Args:
        ContextInfo: QMT上下文对象
        stock_code: 股票代码，如果为None则查询所有
        order_type: 订单类型 'ALL', 'BUY', 'SELL'
        
    Returns:
        list: 委托单列表
    """
    try:
        # 获取账户ID
        account_id = get_main_account_id(ContextInfo)
        
        # 查询委托数据
        orders_data = get_trade_detail_data(account_id, 'stock', 'order')
        
        if not orders_data:
            return []
            
        orders_list = []
        
        for order in orders_data:
            try:
                # 获取订单信息
                order_stock = getattr(order, 'm_strInstrumentID', '') + '.' + getattr(order, 'm_strExchangeID', '')
                order_id = getattr(order, 'm_strOrderSysID', '')
                op_type = getattr(order, 'm_nDirection', 0)  # 0=买入, 1=卖出
                order_status = getattr(order, 'm_nOrderStatus', 0)
                price = getattr(order, 'm_dLimitPrice', 0)
                total_volume = getattr(order, 'm_nVolumeTotalOriginal', 0)
                traded_volume = getattr(order, 'm_nVolumeTraded', 0)
                
                # 过滤股票代码
                if stock_code and order_stock != stock_code:
                    continue
                    
                # 判断订单类型
                op_type_desc = '买入' if op_type == 0 else '卖出'
                
                # 过滤订单类型
                if order_type == 'BUY' and op_type != 0:
                    continue
                elif order_type == 'SELL' and op_type != 1:
                    continue
                    
                # 判断是否为未成交订单
                is_pending = (order_status in [0, 1, 2]) and (traded_volume < total_volume)
                
                order_info = {
                    'order_id': order_id,
                    'stock_code': order_stock,
                    'op_type': op_type,
                    'op_type_desc': op_type_desc,
                    'price': price,
                    'total_volume': total_volume,
                    'traded_volume': traded_volume,
                    'remaining_volume': total_volume - traded_volume,
                    'order_status': order_status,
                    'is_pending': is_pending
                }
                
                orders_list.append(order_info)
                
            except Exception as e:
                print(f"⚠️ 解析订单数据异常: {e}")
                continue
                
        return orders_list
        
    except Exception as e:
        print(f"❌ 查询委托单异常: {e}")
        return []

def cancel_orders(ContextInfo, order_ids):
    """
    撤销委托单
    
    Args:
        ContextInfo: QMT上下文对象
        order_ids: 订单ID列表
        
    Returns:
        dict: 撤单结果 {'cancel_success': int, 'cancel_failed': int, 'details': list}
    """
    try:
        if not order_ids:
            return {'cancel_success': 0, 'cancel_failed': 0, 'details': []}
            
        print(f"🔄 开始撤销委托单: {len(order_ids)}个")
        
        # 获取账户ID
        account_id = get_main_account_id(ContextInfo)
        
        cancel_success = 0
        cancel_failed = 0
        details = []
        
        for order_id in order_ids:
            try:
                # 执行撤单
                cancel_result = passorder(
                    25,  # 撤单
                    order_id,
                    account_id,
                    '',  # 撤单不需要股票代码
                    0,
                    0,
                    ContextInfo
                )
                
                if cancel_result and len(cancel_result) > 0:
                    result_info = cancel_result[0]
                    if result_info.get('order_id'):
                        cancel_success += 1
                        details.append({'order_id': order_id, 'status': 'success'})
                        print(f"✅ 撤单成功: {order_id}")
                    else:
                        cancel_failed += 1
                        error_msg = result_info.get('error_msg', '未知错误')
                        details.append({'order_id': order_id, 'status': 'failed', 'error': error_msg})
                        print(f"❌ 撤单失败: {order_id} - {error_msg}")
                else:
                    cancel_failed += 1
                    details.append({'order_id': order_id, 'status': 'failed', 'error': '无返回结果'})
                    print(f"❌ 撤单失败: {order_id} - 无返回结果")
                    
                # 撤单间隔
                time.sleep(0.1)
                
            except Exception as e:
                cancel_failed += 1
                details.append({'order_id': order_id, 'status': 'failed', 'error': str(e)})
                print(f"❌ 撤单异常: {order_id} - {e}")
                
        result = {
            'cancel_success': cancel_success,
            'cancel_failed': cancel_failed,
            'details': details
        }
        
        print(f"📊 撤单完成: 成功{cancel_success}个，失败{cancel_failed}个")
        return result
        
    except Exception as e:
        print(f"❌ 撤单操作异常: {e}")
        return {'cancel_success': 0, 'cancel_failed': len(order_ids), 'details': []}

def check_pending_orders(ContextInfo, stock_code):
    """
    检查是否有未成交的委托单
    
    Args:
        ContextInfo: QMT上下文对象
        stock_code: 股票代码
        
    Returns:
        bool: 是否有未成交委托单
    """
    try:
        orders = query_orders(ContextInfo, stock_code)
        pending_orders = [order for order in orders if order['is_pending']]
        return len(pending_orders) > 0
        
    except Exception as e:
        print(f"❌ 检查未成交委托异常: {e}")
        return False

# ============================================================================
# 持仓信息查询函数
# ============================================================================

def get_current_position_info(C, stock_code):
    """
    获取当前股票的持仓信息 - 参考交易实时主推示例
    
    Args:
        C: QMT上下文对象
        stock_code: 股票代码
        
    Returns:
        dict: 持仓信息
    """
    try:
        # 使用正确的账户ID属性
        account_id = getattr(C, 'acct', 'test_account')
        position_info = get_trade_detail_data(account_id, 'stock', 'position')
        
        if not position_info:
            return {'has_position': False, 'volume': 0, 'avg_price': 0}
            
        for pos in position_info:
            # 参考示例中的属性访问方式
            pos_stock = getattr(pos, 'm_strInstrumentID', '') + '.' + getattr(pos, 'm_strExchangeID', '')
            if pos_stock == stock_code:
                volume = getattr(pos, 'm_nVolume', 0)
                avg_price = getattr(pos, 'm_dOpenPrice', 0)  # 使用成本价
                position_cost = getattr(pos, 'm_dPositionCost', 0)
                position_profit = getattr(pos, 'm_dPositionProfit', 0)
                
                return {
                    'has_position': volume > 0,
                    'volume': volume,
                    'avg_price': avg_price,
                    'position_cost': position_cost,
                    'position_profit': position_profit,
                    'market_value': getattr(pos, 'm_dInstrumentValue', 0)
                }
                
        return {'has_position': False, 'volume': 0, 'avg_price': 0}
        
    except Exception as e:
        print(f"❌ 获取持仓信息异常: {e}")
        return {'has_position': False, 'volume': 0, 'avg_price': 0}

# ============================================================================
# 强制平仓函数
# ============================================================================

def force_close_all_positions(ContextInfo, reason="强制平仓"):
    """
    强制平仓所有持仓
    
    Args:
        ContextInfo: QMT上下文对象
        reason: 平仓原因
        
    Returns:
        dict: 平仓结果
    """
    try:
        print(f"\n🚨 开始强制平仓: {reason}")
        
        # 获取账户ID
        account_id = get_main_account_id(ContextInfo)
        
        # 获取所有持仓
        position_info = get_trade_detail_data(account_id, 'stock', 'position')
        
        if not position_info:
            return {'success': True, 'message': '无持仓需要平仓', 'closed_count': 0}
            
        closed_count = 0
        failed_count = 0
        
        for pos in position_info:
            try:
                volume = getattr(pos, 'm_nVolume', 0)
                if volume <= 0:
                    continue
                    
                stock_code = getattr(pos, 'm_strInstrumentID', '') + '.' + getattr(pos, 'm_strExchangeID', '')
                
                # 获取当前价格（简化处理，使用成本价）
                current_price = getattr(pos, 'm_dOpenPrice', 0)
                
                print(f"📤 强制平仓: {stock_code} {volume}股")
                
                # 执行卖出
                sell_result = execute_sell_order(ContextInfo, stock_code, current_price, 
                                               volume=volume, reason=reason, auto_cancel_pending=True)
                
                if sell_result['success']:
                    closed_count += 1
                    print(f"✅ 强制平仓成功: {stock_code}")
                else:
                    failed_count += 1
                    print(f"❌ 强制平仓失败: {stock_code} - {sell_result['reason']}")
                    
            except Exception as e:
                failed_count += 1
                print(f"❌ 强制平仓异常: {e}")
                
        result = {
            'success': failed_count == 0,
            'message': f"强制平仓完成: 成功{closed_count}个，失败{failed_count}个",
            'closed_count': closed_count,
            'failed_count': failed_count
        }
        
        print(f"📊 {result['message']}")
        return result
        
    except Exception as e:
        print(f"❌ 强制平仓异常: {e}")
        return {'success': False, 'message': f'强制平仓异常: {e}', 'closed_count': 0}

# ============================================================================
# 工具函数
# ============================================================================

def get_main_account_id(ContextInfo):
    """
    获取主图账户ID
    
    Args:
        ContextInfo: QMT上下文对象
        
    Returns:
        str: 账户ID
    """
    try:
        # 优先级顺序：accountid > account全局变量 > 默认值
        if hasattr(ContextInfo, 'accountid') and ContextInfo.accountid:
            print(f"📊 使用主图账户ID: {ContextInfo.accountid}")
            return ContextInfo.accountid
        elif 'account' in globals():
            account_id = globals()['account']
            print(f"📊 使用全局账户ID: {account_id}")
            return account_id
        else:
            print(f"📊 使用默认账户ID: *********")
            return '*********'  # 默认账户ID
    except Exception as e:
        print(f"❌ 获取主图账户ID异常: {e}")
        return '*********'

def manage_pending_orders(ContextInfo, stock_code, current_price):
    """
    管理未成交的委托单 - 实现撤单重新挂单逻辑
    
    Args:
        ContextInfo: QMT上下文对象
        stock_code: 股票代码
        current_price: 当前价格
        
    Returns:
        dict: 处理结果
    """
    try:
        print(f"\n🔍 检查未成交委托单: {stock_code}")
        
        # 查询所有未处理的委托
        pending_orders = query_orders(ContextInfo, stock_code, 'ALL')
        pending_orders = [order for order in pending_orders if order['is_pending']]
        
        if not pending_orders:
            print("✅ 没有未处理的委托单")
            return {'processed': 0, 'cancelled': 0, 'reordered': 0}
            
        print(f"📋 发现 {len(pending_orders)} 个未处理委托单")
        
        processed_count = 0
        cancelled_count = 0
        reordered_count = 0
        
        for order in pending_orders:
            try:
                order_type = order['op_type_desc']
                order_price = order['price']
                order_volume = order['total_volume']
                order_id = order['order_id']
                
                print(f"🔄 处理委托: {order_type} {order_volume}股@{order_price:.3f}")
                
                # 判断是否需要撤单重新挂单
                should_cancel = False
                reason = ""
                
                if order_type == '买入':
                    # 买入委托：未成交单需要撤销（按您的要求）
                    should_cancel = True
                    price_diff_pct = abs(order_price - current_price) / current_price
                    reason = f"买入委托未成交自动撤销 (价格偏离{price_diff_pct:.2%})"
                    
                elif order_type == '卖出':
                    # 卖出委托：撤销后重新挂单（按您的要求）
                    should_cancel = True
                    reason = "卖出委托自动撤单重新挂单"
                    
                if should_cancel:
                    print(f"🔄 撤销委托: {reason}")
                    
                    # 撤销委托
                    cancel_result = cancel_orders(ContextInfo, order_ids=[order_id])
                    if cancel_result['cancel_success'] > 0:
                        cancelled_count += 1
                        print(f"✅ 撤单成功: {order_id}")
                        
                        # 等待撤单完成
                        time.sleep(0.5)
                        
                        # 重新挂单处理
                        if order_type == '买入':
                            # 买入委托：只撤销，不重新挂单（按您的要求）
                            print(f"📤 买入委托已撤销，不重新挂单")
                            
                        elif order_type == '卖出':
                            sell_result = execute_sell_order(ContextInfo, stock_code, current_price,
                                                           volume=order_volume, reason="重新挂单",
                                                           auto_cancel_pending=False)
                            if sell_result['success']:
                                reordered_count += 1
                                print(f"✅ 重新卖出挂单成功")
                            else:
                                print(f"❌ 重新卖出挂单失败: {sell_result['reason']}")
                    else:
                        print(f"❌ 撤单失败: {order_id}")
                        
                processed_count += 1
                
            except Exception as e:
                print(f"❌ 处理委托异常: {e}")
                continue
                
        result = {
            'processed': processed_count,
            'cancelled': cancelled_count,
            'reordered': reordered_count
        }
        
        print(f"📊 委托处理结果: 处理{processed_count}个，撤销{cancelled_count}个，重新挂单{reordered_count}个")
        return result
        
    except Exception as e:
        print(f"❌ 委托单管理异常: {e}")
        return {'processed': 0, 'cancelled': 0, 'reordered': 0}

# ============================================================================
# 模块信息
# ============================================================================

__version__ = "1.0.0"
__author__ = "下单执行模块"
__description__ = """
下单执行模块

主要功能：
1. 买入订单执行
2. 卖出订单执行
3. 委托单查询和管理
4. 订单撤销功能
5. 持仓信息查询
6. 强制平仓功能

核心函数：
- execute_buy_order: 执行买入订单
- execute_sell_order: 执行卖出订单
- query_orders: 查询委托单
- cancel_orders: 撤销委托单
- get_current_position_info: 获取持仓信息
- force_close_all_positions: 强制平仓
- manage_pending_orders: 管理未成交委托单

使用方法：
在QMT中导入此模块，调用相应函数进行订单执行和管理
"""

if __name__ == "__main__":
    print(__description__)
    print(f"版本: {__version__}")
    print("✅ 下单执行模块加载完成")
