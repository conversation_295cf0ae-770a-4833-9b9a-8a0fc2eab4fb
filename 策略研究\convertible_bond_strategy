Params
	Numeric NT(30);//开仓区间周期参数，可转债波动相对较小，缩短周期
	Numeric RS(15);//默认出场参数，可转债止损可以更紧
	Numeric M(0.5); //出场自适应参数，调整幅度减小
	Numeric X(2);//自适应参数的步长，减小调整幅度
	Numeric Fund(100000);//可转债资金量
	Numeric MinLots(1);//最小交易手数，可转债1手=10股

	//CMF背离策略参数
	Numeric EXPMA_M1(25);//EXPMA短期参数
	Numeric EXPMA_M2(70);//EXPMA长期参数
	Numeric SKDJ_N(7);//SKDJ的N参数
	Numeric SKDJ_M(4);//SKDJ的M参数
	Numeric CMF_N(14);//CMF计算周期
	Numeric CMF_M(14);//CMF背离判断周期
	Numeric ResistancePeriod(20);//阻力线计算周期
Vars
	Numeric SA(0.3); //可转债ATR倍数调小
	Series<Numeric> ATRMD;
	Series<Numeric> ATRZZ;
	Series<Numeric> TR;
	Series<Numeric> ATR;
	Series<Numeric> ATRN;
	Series<Numeric> HH;
	Series<Numeric> LL;
	Numeric XY;
	Numeric SY;
	Numeric mids;
	Series<Numeric> TRS;//跟踪止损
	Series<Numeric> Myprice2;
	Series<Numeric> Myprice3;
	Series<Numeric> liQKA;
	Series<Numeric> DliqPoint;
	Series<Numeric> KliqPoint;
	Series<Numeric> HighAfterEntry;
	Series<Numeric> LowAfterEntry;
	Series<Numeric> barcoutN;
	Series<Numeric> barN;
	Series<Numeric> NN;
	Numeric Length(14);
	Numeric Length2(20);
	Numeric S;
	Series<Numeric> HHD;
	Series<Numeric> LLD;
	Series<Numeric> MAHD;
	Series<Numeric> MALD;
	Series<Numeric> CD;
	Series<Numeric> MHCD;
	Series<Numeric> MLCD;
	Series<Numeric> H1;
	Series<Numeric> L1;
	Series<Bool> DD;
	Series<Bool> KK;
	Series<Bool> ZD;
	Series<Numeric> VIX;
	Series<Numeric> VIX_index;
	Series<Numeric> Lots;

	//CMF背离策略指标变量
	Series<Numeric> EXP1;//EXPMA短期
	Series<Numeric> EXP2;//EXPMA长期
	Series<Numeric> RSV;//SKDJ的RSV
	Series<Numeric> K;//SKDJ的K值
	Series<Numeric> D;//SKDJ的D值
	Series<Numeric> CMF;//CMF指标
	Series<Numeric> CLV;//CLV指标
	Series<Numeric> MF;//资金流量
	Series<Numeric> ResistanceLine;//阻力线
	Series<Numeric> WeightedAvg;//K线加权均值
	Series<Numeric> TypicalPrice;//典型价格
	Series<Numeric> PositiveMF;//正资金流量
	Series<Numeric> NegativeMF;//负资金流量
	Series<Numeric> HHV_High;//最高价的最高值
	Series<Numeric> LLV_Low;//最低价的最低值
	Series<Numeric> HHV_CMF;//CMF的最高值
	Series<Numeric> LLV_CMF;//CMF的最低值
	Series<Bool> CMF_BottomDiv;//CMF底背离信号
	Series<Bool> CMF_TopDiv;//CMF顶背离信号
Events
	OnInit()
	{
		//可转债不需要设置期货相关参数
		//AddDataFlag(Enum_Data_RolloverBackWard());	
		//AddDataFlag(Enum_Data_RolloverRealPrice());	
		//AddDataFlag(Enum_Data_AutoSwapPosition());	
		//AddDataFlag(Enum_Data_IgnoreSwapSignalCalc());	
	}
    onBar()
    {
		//可转债手数计算：1手=10股，最小交易单位
		Lots=Max(MinLots,IntPart(Fund/(Close*10*0.1)));	//计算开仓手数，10股为1手

		if(CurrentBar==0)
    	{
			NN=NT;
    		TRS=RS;
    		barN=0;
			S=SA;
    	}

    	//计算CMF背离策略指标
    	//1. 计算EXPMA
    	EXP1 = XAverage(Close, EXPMA_M1);
    	EXP2 = XAverage(Close, EXPMA_M2);
    	PlotNumeric("EXP1", EXP1);
    	PlotNumeric("EXP2", EXP2);

    	//2. 计算SKDJ
    	Numeric LowV = Lowest(Low, SKDJ_N);
    	Numeric HighV = Highest(High, SKDJ_N);
    	RSV = IIF(HighV == LowV, 50, (Close - LowV) / (HighV - LowV) * 100);
    	K = XAverage(RSV, SKDJ_M);
    	D = XAverage(K, SKDJ_M);
    	PlotNumeric("K", K);
    	PlotNumeric("D", D);

    	//3. 计算CMF指标
    	TypicalPrice = (High + Low + Close) / 3;
    	CLV = IIF(High == Low, 0, (Close - Low - High + Close) / (High - Low));
    	MF = CLV * Vol;
    	CMF = Summation(MF, CMF_N) / Summation(Vol, CMF_N);
    	PlotNumeric("CMF", CMF);

    	//4. 计算阻力线
    	WeightedAvg = (High + Low + 2 * Close) / 4;
    	ResistanceLine = WeightedAvg + (WeightedAvg - Low);
    	PlotNumeric("ResistanceLine", ResistanceLine);

    	//5. 检测CMF背离
    	HHV_High = Highest(High, CMF_M);
    	LLV_Low = Lowest(Low, CMF_M);
    	HHV_CMF = Highest(CMF, CMF_M);
    	LLV_CMF = Lowest(CMF, CMF_M);

    	//底背离：价格创新低但CMF未创新低，且CMF<0
    	CMF_BottomDiv = (Low <= LLV_Low) and (CMF > LLV_CMF) and (CMF < 0);
    	//顶背离：价格创新高但CMF未创新高，且CMF>0
    	CMF_TopDiv = (High >= HHV_High) and (CMF < HHV_CMF) and (CMF > 0);
    		//记录开仓后高低点
        If(BarsSinceentry == 0)
        {
            HighAfterEntry = High;
            LowAfterEntry = Low;
        }else
        {
            HighAfterEntry = Min(HighAfterEntry,High); // 空头止损，更新最低的最高价
            LowAfterEntry = Max(LowAfterEntry,Low);    // 多头止损，更新最高的最低价
        }

    	TR=MAX(MAX((HIGH-LOW),ABS(CLOSE[1]-HIGH)),ABS(CLOSE[1]-LOW));   
    	ATR=Average(TR,Length);
    	ATRMD=(ATR/Close)/Summation((ATR/Close),Length2);//计算一定范围的波动率
    	ATRN=ATRMD*100;//百分比化
    	
    	//可转债波动率阈值调整，相对股指期货更小
    	XY=0.02*100; //可转债波动率下限2%
    	SY=0.04*100; //可转债波动率上限4%
    	mids=0.03*100;//可转债波动率中线3%
    	
    	//PlotNumeric("ATRN",ATRN);
    	//PlotNumeric("XY",XY);
    	//PlotNumeric("SY",SY);
    	//PlotNumeric("mids",mids);

    	if(CurrentBar>barN)
    	{
			If(MarketPosition<>0)
			{//持仓波动率调整
				if(ATRN[1]>SY) //当波动率大于上限时，波动率逐步走向过热
				{
					if (ATRN[1]>ATRN[2])//波动率持续放大
					{
						TRS=TRS-M;
						TRS=Max(TRS,3); //可转债最小止损调整为3
					}
				}
				if(ATRN[1]<XY)//当波动率小于下限时，波动率逐步走向收缩。
				{	
					 if(ATRN[1]<ATRN[2])//波动率持续收缩
					{
						TRS=TRS+M;
						TRS=Min(TRS,15); //可转债最大止损调整为15
					}
				}
			}
			//开仓波动率调节
			if(ATRN[1]>SY) //当波动率大于上限时
			{
				if (ATRN[1]>ATRN[2])
				{
					NN=NN-3; //可转债调整幅度减小
					NN=Max(NN,10);		
					S=S-X;
					S=MAX(S,0.5); //可转债最小ATR倍数
				}
			}
			if(ATRN[1]<XY)//当波动率小于下限时
			{	
				 if(ATRN[1]<ATRN[2])
				{
					NN=NN+3;
					NN=Min(NN,60); //可转债最大周期	
					S=S+X;
					S=MIN(S,10); //可转债最大ATR倍数
				}
			}
		
			BarN=CurrentBar;
		}
    	Commentary("TRS"+Text(TRS));   
    	Commentary("NN"+Text(NN));  
    	Commentary("ATR"+Text(ATR));  		
    	
    	HH=Highest(H,NN)+ATR*S;
		LL=Lowest(L,NN)-ATR*S;
		PlotNumeric("HH",HH);
		PlotNumeric("LL",LL);

		//CMF背离策略买入条件（只做多，不做空）
		//买入条件：
		//1. EXPMA：EXP1>EXP2
		//2. SKDJ的K和D都小于20
		//3. CMF背离：当前或前三个周期内有CMF底背离
		//4. 当价格向上突破阻力线

		Bool Condition1 = EXP1 > EXP2; //EXPMA多头排列
		Bool Condition2 = K < 20 and D < 20; //SKDJ超卖

		//检查当前和前3个周期的CMF底背离
		Bool Condition3 = CMF_BottomDiv or CMF_BottomDiv[1] or CMF_BottomDiv[2] or CMF_BottomDiv[3];

		//价格突破阻力线
		Bool Condition4 = Close > ResistanceLine[1];

		//综合买入条件
		Bool BuyCondition = Condition1 and Condition2 and Condition3 and Condition4 and MarketPosition == 0;

		if (BuyCondition)
		{
			buy(Lots, Open);
			LowAfterEntry = Open;//保存多头开仓价格;
			NN=NT;
			S=SA;
			Commentary("买入信号触发");
			Commentary("EXP1=" + Text(EXP1) + " EXP2=" + Text(EXP2));
			Commentary("K=" + Text(K) + " D=" + Text(D));
			Commentary("CMF=" + Text(CMF) + " 底背离=" + Text(CMF_BottomDiv));
		}

		Commentary("barcoutN"+Text(barcoutN));
		Commentary("BarsSinceEntry"+Text(BarsSinceEntry));
    	If(MarketPosition == 0)   // 自适应参数默认值；
    	{
    		liQKA = 1;
    		barcoutN=0;
    	}Else if(BarsSinceEntry>barcoutN)					 
    	{
    		liQKA = liQKA - 0.05; //可转债衰减速度减慢
    		liQKA = Max(liQKA,0.4); //可转债最小衰减系数提高
    		barcoutN=BarsSinceEntry;
    	}
    	if(MarketPosition>0)
    	{
    	DliqPoint = LowAfterEntry - (Open*TRS/1000)*liQKA; 
    	}
    	if(MarketPosition<0)
    	{
    	KliqPoint = HighAfterEntry + (Open*TRS/1000)*liQKA; 
    	}
    	// 画线
    	Commentary("(Open*TRS/1000)*liQKA"+Text((Open*TRS/1000)*liQKA));

    	// 持有多单时的出场条件（只有多单，无空单）
     	If(MarketPosition >0 And BarsSinceEntry >0  And Low <= DliqPoint[1] and DliqPoint[1]>0 )
    	{
    		Sell(0,Min(Open,DliqPoint[1]));
    		barcoutN=0;
			TRS=RS;
			Commentary("多单止损出场");
    	}
   
}

//------------------------------------------------------------------------
// 编译版本	GS2015.12.25
// 可转债CMF背离策略版本	2024/08/03
// 基于if_vix策略和CMF背离策略修改，适配可转债交易特点
// 主要修改：
// 1. 交易单位改为10股1手
// 2. 买入条件改为CMF背离策略的买入条件：
//    - EXPMA多头排列(EXP1>EXP2)
//    - SKDJ超卖(K<20且D<20)
//    - CMF底背离(当前或前3个周期)
//    - 价格突破阻力线
// 3. 只做多不做空
// 4. 保留原有的波动率自适应止损机制
// 5. 适应可转债较小波动特性
//------------------------------------------------------------------------
