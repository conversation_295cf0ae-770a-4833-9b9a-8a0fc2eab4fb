#coding:gbk
"""
历史数据下载和预处理模块
从QMT止盈止损下单模块中提取的历史数据处理功能

主要功能：
1. 历史数据下载和获取
2. 数据预处理和验证
3. 数据缓冲区管理
4. 实时数据更新
5. 技术指标计算基础数据准备

使用方法：
在QMT中导入此模块，调用相应函数进行数据处理
"""

import numpy as np
import pandas as pd
import datetime
import time
from typing import Dict, List, Tuple, Optional
from collections import deque

# ============================================================================
# 数据缓冲区管理类
# ============================================================================

class MarketDataBuffer:
    """
    市场数据缓冲区管理类 - 实现方案三的核心功能
    支持历史数据预加载和实时数据增量更新
    """

    def __init__(self, buffer_size=100):
        """
        初始化数据缓冲区

        Args:
            buffer_size: 缓冲区大小，默认100根K线
        """
        self.buffer_size = buffer_size

        # 价格数据缓冲区（存储合成后的K线数据）
        self.close_buffer = deque(maxlen=buffer_size)
        self.high_buffer = deque(maxlen=buffer_size)
        self.low_buffer = deque(maxlen=buffer_size)
        self.open_buffer = deque(maxlen=buffer_size)

        # 成交量数据缓冲区
        self.volume_buffer = deque(maxlen=buffer_size)

        # 时间戳缓冲区
        self.time_buffer = deque(maxlen=buffer_size)

        # Tick数据临时缓冲区（用于合成K线）
        self.tick_buffer = deque(maxlen=10)  # 临时存储tick数据
        self.pending_ticks = []  # 等待合成的tick数据

        # 指标缓存
        self.indicators = {}

        # 初始化状态
        self.is_initialized = False

        print(f"📊 数据缓冲区初始化完成，容量: {buffer_size}根K线（基于tick合成）")

    def preload_history_data(self, ContextInfo, stock_code, period='tick', count=100):
        """
        预加载历史数据填充缓冲区 - 专门针对tick数据优化

        Args:
            ContextInfo: QMT上下文对象
            stock_code: 股票代码
            period: 数据周期（默认tick）
            count: 获取数量
        """
        try:
            print(f"📥 开始预加载历史数据: {stock_code}, 周期: {period}, 数量: {count}")

            # 针对tick数据的特殊处理
            if period == 'tick':
                print(f"  📊 Tick数据模式 - 直接获取最近{count}个tick数据")

                # 直接使用count获取指定数量的tick数据（避免获取过多数据）
                hist_data = ContextInfo.get_market_data_ex(
                    [],  # 获取所有字段（QMT会自动处理tick支持的字段）
                    [stock_code],
                    period='tick',
                    count=count,  # 只获取需要的数量
                    dividend_type='none'  # tick数据不需要复权
                )
            else:
                # 非tick数据的处理（保持原有逻辑）
                hist_data = ContextInfo.get_market_data_ex(
                    [],  # 获取所有字段
                    [stock_code],
                    period=period,
                    count=count,
                    dividend_type='back_ratio'  # 后复权
                )

            if hist_data is None or stock_code not in hist_data:
                print(f"⚠️ 历史数据获取失败，尝试备用方案...")
                # 备用方案：获取最近数据
                backup_fields = [] if period == 'tick' else []
                hist_data = ContextInfo.get_market_data_ex(
                    backup_fields,
                    [stock_code],
                    period=period,
                    count=min(count, 50),  # 减少数量
                    dividend_type='none'
                )

            if hist_data is None or stock_code not in hist_data:
                print(f"❌ 历史数据获取失败，将使用实时数据逐步填充缓冲区")
                return False

            # 解析历史数据
            data_df = hist_data[stock_code]
            data_count = len(data_df)

            if period == 'tick':
                print(f"✅ 成功获取 {data_count} 个历史tick数据")
            else:
                print(f"✅ 成功获取 {data_count} 根历史K线数据")

            # 填充缓冲区 - 针对tick数据特殊处理
            if period == 'tick':
                print(f"📊 处理历史tick数据，将合成K线...")
                # 调试：打印数据结构信息
                print(f"📊 数据列名: {list(data_df.columns) if hasattr(data_df, 'columns') else 'N/A'}")
                if data_count > 0:
                    first_row = data_df.iloc[0]
                    print(f"📊 第一行数据字段: {list(first_row.keys()) if hasattr(first_row, 'keys') else 'N/A'}")

                # Tick数据需要合成K线
                tick_data_list = []

                for i in range(data_count):
                    row = data_df.iloc[i]

                    # 构造tick数据 - 尝试多种可能的字段名
                    # QMT tick数据的字段名可能有变化，尝试多种可能性
                    tick_price = 0
                    for price_field in ['lastPrice', 'last_price', 'price', 'close', 'last']:
                        if price_field in row and row[price_field] is not None:
                            tick_price = float(row[price_field])
                            break

                    tick_volume = 0
                    for volume_field in ['volume', 'lastVolume', 'last_volume', 'vol']:
                        if volume_field in row and row[volume_field] is not None:
                            tick_volume = float(row[volume_field])
                            break

                    time_val = row.get('time', row.get('timetag', ''))

                    tick_data_list.append({
                        'price': tick_price,
                        'volume': tick_volume,
                        'time': time_val
                    })

                # 将tick数据两两合成K线
                kline_count = 0
                for i in range(0, len(tick_data_list) - 1, 2):
                    if i + 1 < len(tick_data_list):
                        # 合成K线
                        tick_pair = tick_data_list[i:i+2]
                        kline_data = self._merge_ticks_to_kline(tick_pair)

                        # 添加到缓冲区
                        self._add_kline_to_buffer(kline_data)
                        kline_count += 1

                print(f"✅ 历史tick数据合成完成：{data_count}个tick → {kline_count}根K线")

            else:
                # K线数据处理（原有逻辑）
                for i in range(data_count):
                    row = data_df.iloc[i]

                    self.close_buffer.append(float(row.get('close', row.get('lastPrice', 0))))
                    self.high_buffer.append(float(row.get('high', row.get('lastPrice', 0))))
                    self.low_buffer.append(float(row.get('low', row.get('lastPrice', 0))))
                    self.open_buffer.append(float(row.get('open', row.get('lastPrice', 0))))
                    self.volume_buffer.append(float(row.get('volume', 0)))

                    # 时间戳处理
                    time_val = row.get('time', row.get('timetag', ''))
                    self.time_buffer.append(str(time_val))

            self.is_initialized = True
            print(f"📊 缓冲区预加载完成，当前数据量: {len(self.close_buffer)}根K线")
            return True

        except Exception as e:
            print(f"❌ 历史数据预加载异常: {e}")
            import traceback
            traceback.print_exc()
            return False

    def update_realtime_data(self, close_price, high_price=None, low_price=None,
                           open_price=None, volume=0, timestamp=None):
        """
        更新实时数据到缓冲区

        对于tick数据，推荐使用add_tick_data方法来自动合成K线
        此方法保留用于直接添加K线数据的兼容性

        Args:
            close_price: 收盘价/当前价
            high_price: 最高价
            low_price: 最低价
            open_price: 开盘价
            volume: 成交量
            timestamp: 时间戳
        """
        # 添加新数据到缓冲区（自动维护大小）
        self.close_buffer.append(float(close_price))
        self.high_buffer.append(float(high_price or close_price))
        self.low_buffer.append(float(low_price or close_price))
        self.open_buffer.append(float(open_price or close_price))
        self.volume_buffer.append(float(volume))
        self.time_buffer.append(str(timestamp or datetime.datetime.now()))

        # 清除过期的指标缓存
        self.indicators.clear()

    def update_tick_data(self, tick_price, tick_volume, timestamp=None):
        """
        更新tick数据（推荐用于tick策略）
        自动将tick数据合成K线后添加到缓冲区

        Args:
            tick_price: tick价格
            tick_volume: tick成交量
            timestamp: 时间戳
        """
        self.add_tick_data(tick_price, tick_volume, timestamp)

    def calculate_ma(self, period):
        """计算移动平均线"""
        if len(self.close_buffer) < period:
            return None

        cache_key = f'ma_{period}'
        if cache_key not in self.indicators:
            recent_prices = list(self.close_buffer)[-period:]
            self.indicators[cache_key] = sum(recent_prices) / period

        return self.indicators[cache_key]

    def add_tick_data(self, tick_price, tick_volume, timestamp=None):
        """
        添加tick数据并尝试合成K线

        Args:
            tick_price: tick价格
            tick_volume: tick成交量
            timestamp: 时间戳
        """
        try:
            # 构造tick数据
            tick_data = {
                'price': tick_price,
                'volume': tick_volume,
                'time': timestamp or datetime.datetime.now()
            }

            # 添加到tick缓冲区
            self.pending_ticks.append(tick_data)

            # 检查是否可以合成K线（QMT tick是3秒一个，两个tick合成一个K线）
            if len(self.pending_ticks) >= 2:
                # 合成K线
                kline_data = self._merge_ticks_to_kline(self.pending_ticks[:2])

                # 将合成的K线添加到缓冲区
                self._add_kline_to_buffer(kline_data)

                # 移除已合成的tick
                self.pending_ticks = self.pending_ticks[2:]

                # 清除指标缓存
                self.indicators.clear()

                # 显示详细的成交量计算信息
                original_vols = kline_data.get('original_volumes', [])
                if len(original_vols) >= 2:
                    print(f"📊 Tick合成K线: OHLC[{kline_data['open']:.3f}, {kline_data['high']:.3f}, {kline_data['low']:.3f}, {kline_data['close']:.3f}]")
                    print(f"   📈 成交量计算: {original_vols[1]:.0f} - {original_vols[0]:.0f} = {kline_data['volume']:.0f} (增量)")
                else:
                    print(f"📊 Tick合成K线: OHLC[{kline_data['open']:.3f}, {kline_data['high']:.3f}, {kline_data['low']:.3f}, {kline_data['close']:.3f}], 成交量={kline_data['volume']:.0f}")

        except Exception as e:
            print(f"❌ Tick数据处理异常: {e}")

    def _merge_ticks_to_kline(self, tick_list):
        """
        将tick数据合成为K线

        Args:
            tick_list: tick数据列表

        Returns:
            dict: 合成的K线数据
        """
        if len(tick_list) < 2:
            raise ValueError("至少需要2个tick数据来合成K线")

        # 计算OHLC
        prices = [tick['price'] for tick in tick_list]
        open_price = tick_list[0]['price']
        close_price = tick_list[-1]['price']
        high_price = max(prices)
        low_price = min(prices)

        # 计算增量成交量（tick数据是累积成交量，需要计算差值）
        # 第一个tick的增量 = 当前累积 - 上一个K线的累积（如果有的话）
        # 第二个tick的增量 = 第二个tick累积 - 第一个tick累积
        if len(tick_list) == 2:
            # 两个tick的情况：第二个tick的累积成交量 - 第一个tick的累积成交量
            volume_increment = tick_list[1]['volume'] - tick_list[0]['volume']
            # 如果增量为负或为0，使用最小增量
            if volume_increment <= 0:
                volume_increment = max(1, tick_list[1]['volume'] / 1000)  # 保守估算
        else:
            # 多个tick的情况：最后一个 - 第一个
            volume_increment = tick_list[-1]['volume'] - tick_list[0]['volume']
            if volume_increment <= 0:
                volume_increment = max(1, tick_list[-1]['volume'] / 1000)

        return {
            'open': open_price,
            'high': high_price,
            'low': low_price,
            'close': close_price,
            'volume': volume_increment,  # 使用增量成交量
            'start_time': tick_list[0]['time'],
            'end_time': tick_list[-1]['time'],
            'original_volumes': [tick['volume'] for tick in tick_list]  # 保留原始累积成交量用于调试
        }

    def _add_kline_to_buffer(self, kline_data):
        """
        将K线数据添加到缓冲区

        Args:
            kline_data: K线数据字典
        """
        self.open_buffer.append(kline_data['open'])
        self.high_buffer.append(kline_data['high'])
        self.low_buffer.append(kline_data['low'])
        self.close_buffer.append(kline_data['close'])
        self.volume_buffer.append(kline_data['volume'])
        self.time_buffer.append(str(kline_data.get('end_time', '')))

    def calculate_atr(self, period=14):
        """计算ATR（平均真实波幅）"""
        if len(self.close_buffer) < period + 1:
            return None

        cache_key = f'atr_{period}'
        if cache_key not in self.indicators:
            highs = list(self.high_buffer)[-period-1:]
            lows = list(self.low_buffer)[-period-1:]
            closes = list(self.close_buffer)[-period-1:]

            true_ranges = []
            for i in range(1, len(highs)):
                tr1 = highs[i] - lows[i]
                tr2 = abs(highs[i] - closes[i-1])
                tr3 = abs(lows[i] - closes[i-1])
                true_ranges.append(max(tr1, tr2, tr3))

            if true_ranges:
                self.indicators[cache_key] = sum(true_ranges) / len(true_ranges)
            else:
                self.indicators[cache_key] = 0

        return self.indicators[cache_key]

    def get_current_price(self):
        """获取当前价格"""
        return self.close_buffer[-1] if self.close_buffer else None

    def get_current_volume(self):
        """获取当前成交量"""
        return self.volume_buffer[-1] if self.volume_buffer else 0

    def get_data_count(self):
        """获取缓冲区数据量"""
        return len(self.close_buffer)

    def is_ready_for_trading(self, min_periods=20):
        """检查是否有足够数据进行交易"""
        return len(self.close_buffer) >= min_periods

# ============================================================================
# 历史数据下载函数
# ============================================================================

def download_history_data(stock_code, period, start_date, end_date):
    """
    下载历史数据（QMT标准函数）
    
    Args:
        stock_code: 股票代码
        period: 数据周期
        start_date: 开始日期
        end_date: 结束日期
    """
    try:
        print(f"📥 下载历史数据: {stock_code}, 周期: {period}")
        print(f"   时间范围: {start_date} ~ {end_date}")
        
        # 这里应该调用QMT的历史数据下载函数
        # 具体实现需要根据QMT API文档
        
        return True
    except Exception as e:
        print(f"❌ 历史数据下载失败: {e}")
        return False

def init_strategy_download_only(ContextInfo):
    """
    策略初始化函数 - 仅下载数据（QMT标准模式）
    注意：在init()中只能下载数据，不能获取数据
    """
    print("🚀 QMT止盈止损策略初始化 - 数据缓冲区版本")
    print("="*60)

    # 获取股票代码
    stock_code = ContextInfo.stockcode + '.' + ContextInfo.market
    print(f"📊 目标股票: {stock_code}")
    print(f"🕐 初始化时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # 下载历史数据（QMT要求在init中完成）
    print(f"\n📥 开始下载历史数据...")

    try:
        # 计算下载日期范围
        end_date = datetime.datetime.now().strftime('%Y%m%d')
        start_date = (datetime.datetime.now() - datetime.timedelta(days=10)).strftime('%Y%m%d')

        print(f"📅 下载范围: {start_date} ~ {end_date}")

        # 下载分钟级历史数据
        download_history_data(stock_code, "1m", start_date, end_date)
        print(f"✅ 分钟数据下载完成")

        # 下载日线数据用于长期指标
        download_history_data(stock_code, "1d", start_date, end_date)
        print(f"✅ 日线数据下载完成")

    except Exception as e:
        print(f"⚠️ 历史数据下载异常: {e}")
        print(f"💡 提示: 将在handlebar中逐步填充数据缓冲区")

    # 等待数据写入完成
    print(f"\n⏳ 等待数据写入完成...")
    time.sleep(5)

    # 标记初始化完成，但数据缓冲区将在第一次handlebar中创建
    ContextInfo.init_completed = True
    ContextInfo.buffer_initialized = False

    print(f"\n💡 策略初始化完成，等待handlebar进行数据获取...")
    print("="*60)

# ============================================================================
# 实时数据更新函数
# ============================================================================

def update_buffer_with_current_data_qmt(ContextInfo):
    """
    使用QMT当前数据更新缓冲区 - 支持tick和K线数据
    """
    try:
        # 确保缓冲区存在
        if not hasattr(ContextInfo, 'data_buffer'):
            print("⚠️ 数据缓冲区不存在，跳过更新")
            return

        # 检查是否为tick模式
        period = getattr(ContextInfo, 'period', 'tick')

        if period == 'tick':
            # Tick数据处理 - 使用测试验证成功的方法3
            try:
                stock_code = ContextInfo.stockcode + '.' + ContextInfo.market

                # 使用get_market_data_ex + count=1获取最新tick数据（测试验证成功的方法）
                realtime_data = ContextInfo.get_market_data_ex(
                    [],  # 获取所有字段
                    [stock_code],
                    period='tick',
                    count=1,
                    dividend_type='none'
                )

                if realtime_data and stock_code in realtime_data:
                    data_df = realtime_data[stock_code]
                    if len(data_df) > 0:
                        latest_data = data_df.iloc[-1]

                        # 尝试多种可能的字段名（与历史数据处理保持一致）
                        tick_price = 0
                        for price_field in ['lastPrice', 'last_price', 'price', 'close', 'last']:
                            if price_field in latest_data and latest_data[price_field] is not None:
                                tick_price = float(latest_data[price_field])
                                break

                        tick_volume = 0
                        for volume_field in ['volume', 'lastVolume', 'last_volume', 'vol']:
                            if volume_field in latest_data and latest_data[volume_field] is not None:
                                tick_volume = float(latest_data[volume_field])
                                break

                        if tick_price > 0:
                            # 使用tick数据更新方法
                            ContextInfo.data_buffer.update_tick_data(tick_price, tick_volume)
                        else:
                            print(f"⚠️ 无法获取有效的tick价格数据，可用字段: {list(latest_data.keys())}")
                            return
                    else:
                        print("⚠️ 实时tick数据为空")
                        return
                else:
                    print("⚠️ 无法获取实时tick数据")
                    return
            except Exception as e:
                print(f"⚠️ 获取实时tick数据失败: {e}")
                return
        else:
            # K线数据处理（原有逻辑）
            try:
                # 方法1：直接从ContextInfo获取
                if hasattr(ContextInfo, 'close') and len(ContextInfo.close) > 0:
                    current_price = ContextInfo.close[-1]
                    high_price = ContextInfo.high[-1] if hasattr(ContextInfo, 'high') else current_price
                    low_price = ContextInfo.low[-1] if hasattr(ContextInfo, 'low') else current_price
                    open_price = ContextInfo.open[-1] if hasattr(ContextInfo, 'open') else current_price
                    volume = ContextInfo.vol[-1] if hasattr(ContextInfo, 'vol') else 0
                else:
                    print("⚠️ 无法获取K线市场数据")
                    return

            except Exception as e:
                print(f"⚠️ 获取K线数据失败: {e}")
                return

            # 获取时间戳
            try:
                timestamp = ContextInfo.get_bar_timetag(ContextInfo.barpos)
            except:
                timestamp = datetime.datetime.now()

            # 更新缓冲区（只在K线模式下）
            ContextInfo.data_buffer.update_realtime_data(
                close_price=current_price,
                high_price=high_price,
                low_price=low_price,
                open_price=open_price,
                volume=volume,
                timestamp=timestamp
            )

        # 计算技术指标（tick和K线模式都需要）
        ma5 = ContextInfo.data_buffer.calculate_ma(5)
        ma20 = ContextInfo.data_buffer.calculate_ma(20)
        atr = ContextInfo.data_buffer.calculate_atr(14)

        # 获取当前价格（从缓冲区）
        current_price = ContextInfo.data_buffer.get_current_price()
        if current_price is None:
            current_price = 0

        # 存储到上下文
        ContextInfo.ma5 = ma5
        ContextInfo.ma20 = ma20
        ContextInfo.atr = atr
        ContextInfo.current_price = current_price

        # 调试信息
        if getattr(ContextInfo, 'debug_mode', False):
            print(f"📊 数据更新: 价格={current_price:.3f}, 缓冲区={ContextInfo.data_buffer.get_data_count()}根K线")
            if ma5 and ma20:
                print(f"📈 指标: MA5={ma5:.3f}, MA20={ma20:.3f}, ATR={atr:.3f if atr else 'N/A'}")

    except Exception as e:
        print(f"❌ 缓冲区更新异常: {e}")
        import traceback
        traceback.print_exc()

# ============================================================================
# 安全数据获取函数
# ============================================================================

def get_safe_market_data_for_trading(C):
    """
    安全获取市场数据 - 使用正确的QMT API方法

    参数:
        C: 策略上下文

    返回:
        tuple: (价格, 成交量) 或 None
    """
    try:
        # 使用QMT标准API获取数据（与6sk线文件一致的方法）
        local_data = C.get_market_data_ex(
            ['lastPrice', 'volume'],
            [C.stock],
            period=C.period,
            count=1,
            subscribe=False
        )

        # 数据有效性检查
        if not local_data or C.stock not in local_data:
            print(f"⚠️ 无法获取{C.stock}的市场数据")
            return None

        stock_data = local_data[C.stock]

        # 修复pandas Series布尔值判断错误 - 使用安全的数据检查方法
        def is_data_valid(data):
            """检查数据是否有效（支持pandas Series、numpy数组、列表等）"""
            if data is None:
                return False
            # 检查pandas Series
            if hasattr(data, 'empty'):
                return not data.empty
            # 检查numpy数组或列表
            if hasattr(data, '__len__'):
                return len(data) > 0
            return True

        # 检查lastPrice数据
        last_price_data = stock_data.get('lastPrice')
        if not is_data_valid(last_price_data):
            print(f"⚠️ lastPrice数据无效")
            return None

        # 检查volume数据
        volume_data = stock_data.get('volume')
        if not is_data_valid(volume_data):
            print(f"⚠️ volume数据无效")
            return None

        # 安全提取数据
        try:
            last_price = round(float(last_price_data[0]), 3)
            volume = float(volume_data[0])
        except (IndexError, TypeError, ValueError) as e:
            print(f"⚠️ 数据提取失败: {e}")
            return None

        # 价格合理性检查
        if last_price <= 0:
            print(f"⚠️ 异常价格: {last_price}")
            return None

        print(f"📊 市场数据获取成功: 价格={last_price}, 成交量={volume}")
        return (last_price, volume)

    except Exception as e:
        print(f"❌ 数据获取失败: {e}")
        return None

# ============================================================================
# 模块信息
# ============================================================================

__version__ = "1.0.0"
__author__ = "历史数据下载和预处理模块"
__description__ = """
历史数据下载和预处理模块

主要功能：
1. 历史数据下载和获取
2. 数据预处理和验证  
3. 数据缓冲区管理
4. 实时数据更新
5. 技术指标计算基础数据准备

核心类：
- MarketDataBuffer: 数据缓冲区管理类

核心函数：
- init_strategy_download_only: 策略初始化和数据下载
- update_buffer_with_current_data_qmt: 实时数据更新
- get_safe_market_data_for_trading: 安全数据获取

使用方法：
在QMT中导入此模块，调用相应函数进行数据处理
"""

if __name__ == "__main__":
    print(__description__)
    print(f"版本: {__version__}")
    print("✅ 历史数据下载和预处理模块加载完成")
