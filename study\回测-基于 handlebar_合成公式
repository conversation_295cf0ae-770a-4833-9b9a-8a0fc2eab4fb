#coding:gbk


# 导入常用数据处理和技术分析库
import pandas as pd  # 数据处理库，用于处理时间序列数据
import numpy as np   # 数值计算库，用于数学运算和统计计算
import talib         # 技术分析库，提供各种技术指标计算函数

def init(C):

    # 构建完整的股票代码，格式：股票代码.市场代码（如：000001.SZ）
    C.stock = C.stockcode + '.' + C.market

    # 设置回测账户ID，回测模式下可以使用任意字符串作为账户标识
    C.accountid = "testS"

    # 初始化tick数据缓冲区
    C.tick_buffer = []  # 存储待合成的tick数据
    C.last_volume = 0   # 记录上一个累积成交量
    # 构建空的DataFrame
    C.merged_df = pd.DataFrame(columns=['stime', 'Open', 'High', 'Low', 'Close', 'Volume'])
    # 将stime重新设置为索引
    C.merged_df.set_index('stime', inplace=True)
    # 定义需要计算指标的最小K线数量
    C.min_bars_for_indicators = 50
"""
# 数据预热（这一段在回测阶段可以不使用）===============================================================================
    # 计算指标的k线数量 
    indicator_bars = 50
    tick_count = indicator_bars * 2

    # 下载指定数量的tick数据
    

    # 获取所需要的tick数据
    hist_data = C.get_market_data_ex(
        [],  # 获取所有字段（QMT会自动处理tick支持的字段）
        [C.stock],
        period=C.period,
        count=tick_count,  # 只获取需要的数量
        dividend_type='none'  # tick数据不需要复权
    )
    # 提取 lastPrice和volume 
    price = hist_data[C.stock][['lastPrice', 'volume']]
    #print(price.tail())# 行情数据查看
    #volume = hist_data['volume']
    #print(f"价格: {price}, 成交量: {volume}")
    #print(hist_data[C.stock].columns)   #列名

    # df是的DataFrame，包含'lastPrice'和'volume'列
    # 重置索引以获取连续的整数索引
    df_reset = price.reset_index()

    # 将累积成交量转换为增量成交量
    df_reset['volume'] = df_reset['volume'].diff().fillna(0)
    # 创建一个分组索引，每两个数据点为一组
    group_index = df_reset.index // 2

    #print(df_reset)
    # 使用groupby和agg方法进行聚合
    merged_df = df_reset.groupby(group_index).agg({
        'stime': 'first',           # 时间取每组第一个
        'lastPrice': ['first', 'max', 'min', 'last'],  # 生成Open, High, Low, Close
        'volume': 'sum'             # 成交量求和
    })

    # 展平列名
    merged_df.columns = ['stime', 'Open', 'High', 'Low', 'Close', 'Volume']


    merged_df['stime'] = pd.to_datetime(merged_df['stime'], format='%Y%m%d%H%M%S.%f')
    # 将stime重新设置为索引
    merged_df.set_index('stime', inplace=True)
    print(len(merged_df))
    print(merged_df.tail())   # 显示部分
    #print(merged_df)          # 显示全部
    C.merged_df = merged_df  # 将合成的历史数据保存到C对象中
"""

def handlebar(C):
    # === 第一步：获取当前K线时间信息 ===
    #return
    # 获取当前K线的时间戳并转换为可读格式
    # C.barpos: 当前K线在数据序列中的位置索引
    # C.get_bar_timetag(): 获取指定位置K线的时间戳
    # timetag_to_datetime(): 将时间戳转换为日期时间字符串
    bar_date = timetag_to_datetime(C.get_bar_timetag(C.barpos), '%Y%m%d%H%M%S')
    #print(bar_date)

    # 获取所需要的tick数据
    hist_data = C.get_market_data_ex(
        [],  # 获取所有字段（QMT会自动处理tick支持的字段）
        [C.stock],
        period=C.period,
        end_time=bar_date,
        count=1,  # 回测中取每次最新的1个数据
        dividend_type='none'  # tick数据不需要复权
        )
    # 提取 lastPrice和volume 
    price = hist_data[C.stock][['lastPrice', 'volume']]
    #print(price)
    #print('========================================')
    # 每两个数据合成一个并添加到merged_df中
    # 处理当前tick数据
    current_tick = price.iloc[0]  # 获取当前tick数据
    current_time = pd.to_datetime(bar_date, format='%Y%m%d%H%M%S')
    current_price = current_tick['lastPrice']
    current_volume_cumulative = current_tick['volume']

    # 计算增量成交量
    volume_increment = current_volume_cumulative - C.last_volume
    C.last_volume = current_volume_cumulative

    # 创建tick数据字典
    tick_data = {
        'time': current_time,
        'price': current_price,
        'volume': volume_increment
    }

    # 添加到缓冲区
    C.tick_buffer.append(tick_data)

    # 当缓冲区有2个tick数据时，进行合成
    if len(C.tick_buffer) >= 2:
        # 合成新的K线
        new_kline = {
            'stime': C.tick_buffer[0]['time'],
            'Open': C.tick_buffer[0]['price'],
            'High': max(C.tick_buffer[0]['price'], C.tick_buffer[1]['price']),
            'Low': min(C.tick_buffer[0]['price'], C.tick_buffer[1]['price']),
            'Close': C.tick_buffer[1]['price'],
            'Volume': C.tick_buffer[0]['volume'] + C.tick_buffer[1]['volume']
        }
        
        # 添加到merged_df
        new_row = pd.DataFrame([new_kline])
        new_row.set_index('stime', inplace=True)
        C.merged_df = pd.concat([C.merged_df, new_row])
        
        # 清空缓冲区
        C.tick_buffer = []
        
        # 可选：限制数据长度，保持固定窗口
        if len(C.merged_df) > 100:  # 保持最近100个合成K线
            C.merged_df = C.merged_df.tail(100)
        
        #print(f"新合成K线: {new_kline}")
        print(C.merged_df.tail())
        print(f"已有{len(C.merged_df)}根K线")

        #判断是否有足够的k线进行指标计算
        if len(C.merged_df) >= C.min_bars_for_indicators:
            print("已有足够的K线进行指标计算")
            # 计算交易指标和交易逻辑



