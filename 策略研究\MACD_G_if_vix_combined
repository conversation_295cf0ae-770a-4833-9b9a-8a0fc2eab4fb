//------------------------------------------------------------------------
// 简称: MACD_G_if_vix_combined
// 名称: MACD_G入场逻辑 + if_vix出场逻辑组合策略
// 类别: 公式应用
// 类型: 用户应用
// 输出: Void
//------------------------------------------------------------------------
/*策略思想：
结合MACD_G的多头入场逻辑和if_vix的动态出场逻辑

多头进场逻辑（来自MACD_G）：
1. 最近一个MACD信号是金叉，突破周期内高点
2. 出现底背离时，开多

出场逻辑（来自if_vix）：
使用动态吊灯止损，根据持仓时间和波动率自适应调整
*/

Params
	// MACD_G参数
	Numeric N(2);        // MACD基础周期，2=敏感型，3=稳健型
	Numeric X(5);        // 突破周期，从5调整为6，更稳定
	// if_vix出场参数
	Numeric NT(50);      // 开仓区间周期参数
	Numeric RS(20);      // 默认出场参数，从20调整为25（2.5%止损）
	Numeric M(1);      // 出场自适应参数，从1调整为0.8，更平缓
	Numeric Fund(20000); // 资金量
Vars
	// MACD_G入场相关变量
	Series<Numeric> MACDValue; 
	Series<Numeric> AvgMACD;
	Series<Bool> Cross_up;
	Series<Bool> Cross_dn;
	Series<Numeric> Hbar;
	Series<Numeric> Lbar;
	Series<Numeric> Highup;
	Series<Numeric> Lowdown;
	Series<Numeric> K(0);
	Series<Numeric> HD;
	Series<Numeric> LD;
	Series<Numeric> Bar_Highup;
	Series<Numeric> Bar_Lowdown;
	Series<Numeric> Bar_HD;
	Series<Numeric> Bar_LD;
	Series<Numeric> RHbar;
	Series<Numeric> RLbar;
	Series<Numeric> D_MACD;
	Series<Numeric> K_MACD;
	Series<Numeric> RD_MACD;
	Series<Numeric> RK_MACD;
	Series<Numeric> RHD;
	Series<Numeric> RLD;
	Bool condtion_D;
	Bool condtion_K;
	
	// if_vix出场相关变量
	Numeric SA(0.5); 
	Series<Numeric> ATRMD;
	Series<Numeric> TR;
	Series<Numeric> ATR;
	Series<Numeric> ATRN;
	Numeric XY;
	Numeric SY;
	Numeric mids;
	Series<Numeric> TRS;//跟踪止损 
	Series<Numeric> liQKA;
	Series<Numeric> DliqPoint;
	Series<Numeric> HighAfterEntry;
	Series<Numeric> LowAfterEntry;
	Series<Numeric> barcoutN;
	Series<Numeric> barN;
	Series<Numeric> NN;
	Numeric Length(14);
	Numeric Length2(20); 
	Numeric S;
	Series<Numeric> Lots;

Events
	OnInit()
	{
		AddDataFlag(Enum_Data_RolloverBackWard());	//设置后复权
		AddDataFlag(Enum_Data_RolloverRealPrice());	//设置映射真实价格
		AddDataFlag(Enum_Data_AutoSwapPosition());	//设置自动换仓
		AddDataFlag(Enum_Data_IgnoreSwapSignalCalc());	//设置忽略换仓信号计算
	}
    onBar(ArrayRef<Integer> indexs)
    {
		Lots=Max(1,IntPart(Fund/(O*ContractUnit*BigPointValue*0.1)));	//计算开仓手数

		if(CurrentBar==0)
    	{
			NN=NT;
    		TRS=RS;
    		barN=0;
			S=SA;
    	}
    	
    	//记录开仓后高低点
        If(BarsSinceentry == 0)
        {
            HighAfterEntry = High;
            LowAfterEntry = Low;
        }else
        {
            HighAfterEntry = Min(HighAfterEntry,High); // 空头止损，更新最低的最高价
            LowAfterEntry = Max(LowAfterEntry,Low);    // 多头止损，更新最高的最低价
        }
    	
    	// ========== MACD_G入场逻辑开始 ==========
    	MACDValue = XAverage( Close, 4*N ) - XAverage( Close, 9*N ) ;	//快线
    	AvgMACD = XAverage(MACDValue,3*N);//慢线
    	
    	Cross_up=CrossOver(MACDValue,AvgMACD);//金叉
    	Cross_dn=CrossUnder(MACDValue,AvgMACD);//死叉
    
    	If(Cross_up[1])
    	{
    		RHbar=Hbar;//保存上一次交叉时的位置
    		Hbar=CurrentBar;//记录交叉时的位置
    	}
    	If(Cross_dn[1]) 
    	{
    		RLbar=Lbar;//保存上一次交叉时的位置
    		Lbar=CurrentBar;//记录交叉时的位置
    	}
    	
    	Bar_Highup=C[CurrentBar-Hbar];//回溯金叉时的价格
    	Bar_Lowdown=C[CurrentBar-Lbar];//回溯死叉时的价格
    	Highup=Highest(H[1],Max(Hbar-Lbar,X));//计算金叉时X周期内的最高价
    	Lowdown=Lowest(L[1],Max(Lbar-Hbar,X));//计算死叉时X周期内的最低价
    	
    	//核心计算公式，保存最新和历史的MACD值以及价格高低
    	If(Cross_up[1])
    	{
    		RD_MACD=D_MACD; //保存上一次MACD值
    		RHD=Bar_HD; //保存上一次交叉的价格
    		Bar_HD=Bar_Highup; //赋值本次交叉时的价格
    		D_MACD=AvgMACD; //赋值本次交叉时的DIFF值
    		HD=Highup;//赋值本次交叉时的X周期内的高低价
    	}
    	If(Cross_dn[1])
    	{
    		RK_MACD=K_MACD; //保存上一次MACD值
    		RLD=Bar_LD; //保存上一次交叉的价格
    		Bar_LD=Bar_Lowdown;//赋值本次交叉时的价格
    		K_MACD=AvgMACD;//赋值本次交叉时的DIFF值
    		LD=Lowdown;//赋值本次交叉时的X周期内的高低价
    	}
    	
    	If(Cross_up[2])
    	{
    		K=1;//打开开仓开关
    		condtion_D=Bar_HD<RHD and D_MACD>RD_MACD AND AvgMACD<0 ; //底背离计算公式
    	}
    	If(Cross_dn[2])
    	{
    		K=-1;//打开开仓开关（但我们只做多，所以不使用）
    		condtion_K=Bar_LD>RLD and K_MACD<RK_MACD AND AvgMACD>0; //顶背离计算公式
    	}

    	//正常开仓条件 - 只做多
    	if(K>0 and H>=HD and HD>0 and MarketPosition==0)
    	{
    		Buy(Lots,Max(open,HD));
    		LowAfterEntry = EntryPrice;//保存多头开仓价格;
    	}
    	
    	//背离开仓条件 - 只做多
    	if(condtion_D and MarketPosition==0)
    	{
    		Buy(Lots,open);
    		LowAfterEntry = EntryPrice;//保存多头开仓价格;
    	}
    	// ========== MACD_G入场逻辑结束 ==========
    	
    	// ========== if_vix出场逻辑开始 ==========
    	TR=MAX(MAX((HIGH-LOW),ABS(CLOSE[1]-HIGH)),ABS(CLOSE[1]-LOW));   
    	ATR=Average(TR,Length);
    	ATRMD=(ATR/Close)/Summation((ATR/Close),Length2);//计算一定范围的波动率
    	ATRN=ATRMD*100;//百分比化
    	XY=0.04*100; //通过EXCEL统计的下限
    	SY=0.06*100;//通过EXCEL统计的上限
    	mids=0.05*100;//通过EXCEL统计的中线

    	if(CurrentBar>barN)
    	{
			If(MarketPosition<>0)
			{//持仓波动率调整
				if(ATRN[1]>SY) //当波动率大于上限时，波动率逐步走向过热
				{
					if (ATRN[1]>ATRN[2])//波动率持续放大，说明趋势在加速，行情波动加大。移动止盈参数也随之增大以免被甩出去；
					{
						TRS=TRS-M;
						TRS=Max(TRS,5);
					}
				}
				if(ATRN[1]<XY)//当波动率小于下限时，波动率逐步走向收缩。
				{	
					 if(ATRN[1]<ATRN[2])//波动率持续收缩，说明行情在震荡或者无明显趋势。移动止盈参数也随之减少以免因为参数迟钝在震荡行情亏损过大；
					{
						TRS=TRS+M;
						TRS=Min(TRS,20);
					}
				}
			}
			BarN=CurrentBar;
		}
    	
    	Commentary("TRS"+Text(TRS));   
    	Commentary("NN"+Text(NN));  
    	Commentary("ATR"+Text(ATR));  		

		Commentary("barcoutN"+Text(barcoutN));
		Commentary("BarsSinceEntry"+Text(BarsSinceEntry));
    	If(MarketPosition == 0)   // 自适应参数默认值；
    	{
    		liQKA = 1;
    		barcoutN=0;
    	}Else if(BarsSinceEntry>barcoutN)
    	{
    		liQKA = liQKA - 0.1;
    		liQKA = Max(liQKA,0.3);
    		barcoutN=BarsSinceEntry;
    	}
    	
    	// 使用if_vix的出场逻辑 - 只保留多头出场逻辑
    	if(MarketPosition>0)
    	{
    	DliqPoint = LowAfterEntry - (Open*TRS/1000)*liQKA; 
    	}
    	
    	// 画线
    	Commentary("(Open*TRS/1000)*liQKA"+Text((Open*TRS/1000)*liQKA));

    	// 持有多单时的if_vix出场逻辑
     	If(MarketPosition >0 And BarsSinceEntry >0  And Low <= DliqPoint[1] and DliqPoint[1]>0 ) 
    	{
    		Sell(0,Min(Open,DliqPoint[1]));
    		barcoutN=0;
			TRS=RS;
			K=0; // 重置MACD_G的开仓开关
    	}
    	// ========== if_vix出场逻辑结束 ==========
    	
    	PlotNumeric("HD ",HD );
    	PlotNumeric("LD ",LD );
    	Commentary("K = "+Text(K));
    }

//------------------------------------------------------------------------
// 编译版本	GS2015.12.25
// 用户版本	2024/12/19 组合策略版
// 版权所有	mujinlong1999
// 更改声明	TradeBlazer Software保留对TradeBlazer平台
//			每一版本的TradeBlazer公式修改和重写的权利
// 策略说明：
// 1. 入场逻辑：使用MACD_G的多头入场逻辑
//    - 正常开仓：MACD金叉后突破周期内高点
//    - 背离开仓：MACD底背离时开多
// 2. 出场逻辑：使用if_vix的动态吊灯止损
//    - 根据持仓时间和波动率自适应调整止损点
//    - liQKA参数随持仓时间衰减，止损越来越敏感
// 3. 纯多头策略：只做多不做空，降低风险
//------------------------------------------------------------------------
