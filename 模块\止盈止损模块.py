#coding:gbk
"""
止盈止损模块
从QMT止盈止损下单模块中提取的止盈止损功能

主要功能：
1. ATR动态止损计算
2. 唐奇安通道移动止盈
3. 技术指标计算（MA、ATR、唐奇安通道）
4. 买入信号检测
5. 平仓条件检查
6. 时间风控管理

使用方法：
在QMT中导入此模块，调用相应函数进行止盈止损判断
"""

import numpy as np
import pandas as pd
import datetime
import time
from typing import Dict, List, Tuple, Optional

# 默认策略配置
STRATEGY_CONFIG = {
    'take_profit_ratio': 0.03,  # 3%止盈
    'stop_loss_ratio': 0.02,    # 2%止损
    'atr_period': 14,           # ATR计算周期
    'atr_stop_loss_multiplier': 2.0,    # ATR止损倍数
    'atr_trigger_multiplier': 3.0,      # ATR移动止盈触发倍数
    'donchian_period': 20,      # 唐奇安通道周期
    'ma_short_period': 5,       # 短期均线周期
    'ma_long_period': 20,       # 长期均线周期
    'buy_signal_count_required': 3,     # 买入信号需要的数据次数
    'enable_time_risk_control': True,   # 启用时间风控
    'no_open_after_time': '14:55:00',   # 此时间后不开仓
    'force_close_time': '14:56:00',     # 强制平仓时间
}

# ============================================================================
# 技术指标计算函数
# ============================================================================

def calculate_moving_average(prices, period):
    """
    计算移动平均线
    
    Args:
        prices: 价格数组
        period: 计算周期
        
    Returns:
        float: 移动平均值，如果数据不足返回None
    """
    try:
        if not prices or len(prices) < period:
            return None
            
        recent_prices = prices[-period:]
        return sum(recent_prices) / len(recent_prices)
        
    except Exception as e:
        print(f"❌ 移动平均计算异常: {e}")
        return None

def calculate_atr(highs, lows, closes, period=14):
    """
    计算ATR（平均真实波幅）
    
    Args:
        highs: 最高价数组
        lows: 最低价数组  
        closes: 收盘价数组
        period: 计算周期
        
    Returns:
        float: ATR值，如果数据不足返回None
    """
    try:
        if not all([highs, lows, closes]) or len(closes) < period + 1:
            return None
            
        true_ranges = []
        
        # 计算真实波幅
        for i in range(1, len(closes)):
            if i >= len(highs) or i >= len(lows):
                break
                
            # 三种真实波幅的计算方式
            tr1 = highs[i] - lows[i]  # 当日最高价 - 当日最低价
            tr2 = abs(highs[i] - closes[i-1])  # 当日最高价 - 前日收盘价
            tr3 = abs(lows[i] - closes[i-1])   # 当日最低价 - 前日收盘价
            
            true_range = max(tr1, tr2, tr3)
            true_ranges.append(true_range)
            
        if len(true_ranges) < period:
            return None
            
        # 计算ATR（简单移动平均）
        recent_trs = true_ranges[-period:]
        atr = sum(recent_trs) / len(recent_trs)
        
        return atr
        
    except Exception as e:
        print(f"❌ ATR计算异常: {e}")
        return None

def calculate_donchian_channel(highs, lows, period=20):
    """
    计算唐奇安通道
    
    Args:
        highs: 最高价数组
        lows: 最低价数组
        period: 计算周期
        
    Returns:
        dict: {'upper_channel': float, 'lower_channel': float} 或 None
    """
    try:
        if not highs or not lows or len(highs) < period or len(lows) < period:
            return None
            
        # 计算指定周期内的最高价和最低价
        recent_highs = highs[-period:]
        recent_lows = lows[-period:]
        
        upper_channel = max(recent_highs)
        lower_channel = min(recent_lows)
        
        return {
            'upper_channel': upper_channel,
            'lower_channel': lower_channel,
            'middle_channel': (upper_channel + lower_channel) / 2
        }
        
    except Exception as e:
        print(f"❌ 唐奇安通道计算异常: {e}")
        return None

# ============================================================================
# 买入信号检测函数
# ============================================================================

def check_buy_signal_three_bars(ContextInfo, stock_code, market_data=None):
    """
    检查买入信号 - 简化版本，接收三次数据后买入
    
    Args:
        ContextInfo: QMT上下文对象
        stock_code: 股票代码
        market_data: 市场数据（可选）
        
    Returns:
        dict: {'buy_signal': bool, 'reason': str, 'data_count': int}
    """
    try:
        # 时间风控检查
        time_control = check_time_risk_control(ContextInfo)
        if not time_control['allow_open']:
            return {
                'buy_signal': False,
                'reason': f"时间风控禁止开仓: {time_control['reason']}",
                'data_count': 0
            }
            
        # 初始化数据计数器
        if not hasattr(ContextInfo, 'data_count'):
            ContextInfo.data_count = 0
            
        if not hasattr(ContextInfo, 'buy_signal_triggered'):
            ContextInfo.buy_signal_triggered = False
            
        # 如果已经触发过买入信号，不再重复触发
        if ContextInfo.buy_signal_triggered:
            return {
                'buy_signal': False,
                'reason': "买入信号已触发，避免重复买入",
                'data_count': ContextInfo.data_count
            }
            
        # 增加数据计数
        ContextInfo.data_count += 1
        
        # 检查是否达到要求的数据次数
        required_count = STRATEGY_CONFIG.get('buy_signal_count_required', 3)
        
        if ContextInfo.data_count >= required_count:
            # 触发买入信号
            ContextInfo.buy_signal_triggered = True
            
            return {
                'buy_signal': True,
                'reason': f"数据接收次数达到{required_count}次，触发买入信号",
                'data_count': ContextInfo.data_count
            }
        else:
            return {
                'buy_signal': False,
                'reason': f"等待更多数据，当前{ContextInfo.data_count}次，需要{required_count}次",
                'data_count': ContextInfo.data_count
            }
            
    except Exception as e:
        print(f"❌ 买入信号检查异常: {e}")
        return {
            'buy_signal': False,
            'reason': f"买入信号检查异常: {e}",
            'data_count': getattr(ContextInfo, 'data_count', 0)
        }

# ============================================================================
# 止盈止损检查函数
# ============================================================================

def check_exit_conditions_donchian(ContextInfo, current_price, entry_price, 
                                 highest_price_since_entry, trailing_stop_price, 
                                 market_data=None):
    """
    检查唐奇安移动止盈止损条件
    
    Args:
        ContextInfo: QMT上下文对象
        current_price: 当前价格
        entry_price: 入场价格
        highest_price_since_entry: 入场后最高价
        trailing_stop_price: 当前移动止盈价格
        market_data: 市场数据（包含highs, lows, closes等）
        
    Returns:
        dict: 平仓决策结果
    """
    try:
        result = {
            'should_exit': False,
            'reason': '',
            'new_trailing_stop': trailing_stop_price,
            'new_highest_price': highest_price_since_entry
        }
        
        # 更新最高价
        if current_price > highest_price_since_entry:
            result['new_highest_price'] = current_price
            highest_price_since_entry = current_price
            
        # 计算当前盈亏比例
        profit_ratio = (current_price - entry_price) / entry_price
        profit_percentage = profit_ratio * 100
        
        # 1. ATR动态止损检查
        atr_result = check_atr_stop_loss(ContextInfo, current_price, entry_price, market_data)
        if atr_result['should_exit']:
            result.update(atr_result)
            return result
            
        # 2. 固定止损检查（备用）
        stop_loss_ratio = STRATEGY_CONFIG.get('stop_loss_ratio', 0.02)
        if profit_ratio <= -stop_loss_ratio:
            result['should_exit'] = True
            result['reason'] = f"固定止损触发: 亏损{profit_percentage:.2f}% >= {stop_loss_ratio*100:.1f}%"
            return result
            
        # 3. 移动止盈逻辑
        trailing_result = calculate_moving_profit_control(
            ContextInfo, current_price, entry_price, highest_price_since_entry, 
            trailing_stop_price, market_data
        )
        
        if trailing_result['should_exit']:
            result.update(trailing_result)
            return result
            
        # 4. 更新移动止盈价格
        if 'new_trailing_stop' in trailing_result:
            result['new_trailing_stop'] = trailing_result['new_trailing_stop']
            
        # 5. 继续持仓
        result['reason'] = f"继续持仓: 当前盈亏{profit_percentage:+.2f}%, 最高价{highest_price_since_entry:.3f}"
        
        return result
        
    except Exception as e:
        print(f"❌ 止盈止损检查异常: {e}")
        return {
            'should_exit': False,
            'reason': f"止盈止损检查异常: {e}",
            'new_trailing_stop': trailing_stop_price,
            'new_highest_price': highest_price_since_entry
        }

def check_atr_stop_loss(ContextInfo, current_price, entry_price, market_data=None):
    """
    检查ATR动态止损
    
    Args:
        ContextInfo: QMT上下文对象
        current_price: 当前价格
        entry_price: 入场价格
        market_data: 市场数据
        
    Returns:
        dict: ATR止损检查结果
    """
    try:
        result = {'should_exit': False, 'reason': ''}
        
        # 获取ATR值
        atr = None
        if market_data and all(key in market_data for key in ['highs', 'lows', 'closes']):
            atr = calculate_atr(market_data['highs'], market_data['lows'], 
                              market_data['closes'], STRATEGY_CONFIG['atr_period'])
                              
        # ATR数据不足时的保护机制
        if atr is None or atr <= 0:
            # 使用固定比例作为备用止损
            backup_stop_ratio = 0.005  # 0.5%固定止损
            stop_loss_price = entry_price * (1 - backup_stop_ratio)
            
            if current_price <= stop_loss_price:
                result['should_exit'] = True
                result['reason'] = f"备用止损触发: {current_price:.3f} <= {stop_loss_price:.3f} (ATR数据不足，使用{backup_stop_ratio*100:.1f}%固定止损)"
                result['atr_info'] = {'atr': 0, 'backup_mode': True}
                
            return result
            
        # 计算ATR止损价格
        atr_multiplier = STRATEGY_CONFIG.get('atr_stop_loss_multiplier', 2.0)
        atr_stop_loss_price = entry_price - (atr * atr_multiplier)
        
        # 检查是否触发ATR止损
        if current_price <= atr_stop_loss_price:
            result['should_exit'] = True
            result['reason'] = f"ATR止损触发: {current_price:.3f} <= {atr_stop_loss_price:.3f} (ATR={atr:.3f}×{atr_multiplier})"
            result['atr_info'] = {
                'atr': atr,
                'multiplier': atr_multiplier,
                'stop_price': atr_stop_loss_price
            }
            
        return result
        
    except Exception as e:
        print(f"❌ ATR止损检查异常: {e}")
        return {'should_exit': False, 'reason': f"ATR止损检查异常: {e}"}

def calculate_moving_profit_control(ContextInfo, current_price, entry_price, 
                                  highest_price, trailing_stop_price, market_data=None):
    """
    计算移动止盈控制
    
    Args:
        ContextInfo: QMT上下文对象
        current_price: 当前价格
        entry_price: 入场价格
        highest_price: 最高价
        trailing_stop_price: 当前移动止盈价格
        market_data: 市场数据
        
    Returns:
        dict: 移动止盈控制结果
    """
    try:
        result = {'should_exit': False, 'reason': '', 'new_trailing_stop': trailing_stop_price}
        
        # 计算当前利润
        current_profit = current_price - entry_price
        current_profit_ratio = current_profit / entry_price
        
        # 获取ATR值用于动态阈值计算
        atr = None
        if market_data and all(key in market_data for key in ['highs', 'lows', 'closes']):
            atr = calculate_atr(market_data['highs'], market_data['lows'], 
                              market_data['closes'], STRATEGY_CONFIG['atr_period'])
                              
        # 计算移动止盈触发阈值
        if atr and atr > 0:
            # 使用ATR动态阈值
            atr_trigger_multiplier = STRATEGY_CONFIG.get('atr_trigger_multiplier', 3.0)
            trigger_threshold = atr * atr_trigger_multiplier
            trigger_threshold_ratio = trigger_threshold / entry_price
        else:
            # ATR数据不足时使用固定阈值
            trigger_threshold_ratio = 0.01  # 1%最小触发阈值
            trigger_threshold = entry_price * trigger_threshold_ratio
            
        # 检查是否达到移动止盈触发条件
        if current_profit >= trigger_threshold:
            # 计算新的移动止盈价格
            # 使用唐奇安通道下轨作为移动止盈价格
            if market_data and 'highs' in market_data and 'lows' in market_data:
                donchian = calculate_donchian_channel(market_data['highs'], market_data['lows'], 
                                                    STRATEGY_CONFIG.get('donchian_period', 20))
                if donchian:
                    new_trailing_stop = donchian['lower_channel']
                    
                    # 确保移动止盈价格不会下降（只能上升）
                    if new_trailing_stop > trailing_stop_price:
                        result['new_trailing_stop'] = new_trailing_stop
                        result['reason'] = f"更新移动止盈: {new_trailing_stop:.3f} (唐奇安下轨)"
                        
        # 检查是否触发移动止盈
        if trailing_stop_price > 0 and current_price <= trailing_stop_price:
            result['should_exit'] = True
            result['reason'] = f"移动止盈触发: {current_price:.3f} <= {trailing_stop_price:.3f}"
            
        return result
        
    except Exception as e:
        print(f"❌ 移动止盈计算异常: {e}")
        return {'should_exit': False, 'reason': f"移动止盈计算异常: {e}", 'new_trailing_stop': trailing_stop_price}

# ============================================================================
# 时间风控函数
# ============================================================================

def check_time_risk_control(ContextInfo):
    """
    检查时间风控条件
    
    Args:
        ContextInfo: QMT上下文对象
        
    Returns:
        dict: {'allow_open': bool, 'force_close': bool, 'reason': str}
    """
    try:
        if not STRATEGY_CONFIG.get('enable_time_risk_control', True):
            return {'allow_open': True, 'force_close': False, 'reason': '时间风控已禁用'}
            
        current_time = datetime.datetime.now().time()
        
        # 解析配置的时间
        no_open_after_str = STRATEGY_CONFIG.get('no_open_after_time', '14:55:00')
        force_close_str = STRATEGY_CONFIG.get('force_close_time', '14:56:00')
        
        no_open_after = datetime.datetime.strptime(no_open_after_str, '%H:%M:%S').time()
        force_close_time = datetime.datetime.strptime(force_close_str, '%H:%M:%S').time()
        
        # 检查强制平仓时间
        if current_time >= force_close_time:
            return {
                'allow_open': False,
                'force_close': True,
                'reason': f'已到强制平仓时间 {force_close_str}'
            }
            
        # 检查禁止开仓时间
        if current_time >= no_open_after:
            return {
                'allow_open': False,
                'force_close': False,
                'reason': f'已过开仓截止时间 {no_open_after_str}'
            }
            
        # 正常交易时间
        return {
            'allow_open': True,
            'force_close': False,
            'reason': '正常交易时间'
        }
        
    except Exception as e:
        print(f"❌ 时间风控检查异常: {e}")
        return {'allow_open': True, 'force_close': False, 'reason': f'时间风控检查异常: {e}'}

# ============================================================================
# 策略状态管理函数
# ============================================================================

def update_strategy_state(ContextInfo, stock_code, current_price):
    """
    更新策略状态 - 跟踪持仓后的最高价和移动止盈价格
    
    Args:
        ContextInfo: QMT上下文对象
        stock_code: 股票代码
        current_price: 当前价格
    """
    try:
        # 初始化策略状态
        if not hasattr(ContextInfo, 'strategy_states'):
            ContextInfo.strategy_states = {}
            
        if stock_code not in ContextInfo.strategy_states:
            ContextInfo.strategy_states[stock_code] = {
                'highest_price_since_entry': 0,
                'trailing_stop_price': 0,
                'entry_price': 0,
                'last_update_time': '',
                'bars_since_entry': 0
            }
            
        state = ContextInfo.strategy_states[stock_code]
        
        # 这里需要获取当前持仓信息（需要从下单执行模块导入相关函数）
        # 暂时使用简化逻辑
        has_position = getattr(ContextInfo, 'has_position', False)
        
        if has_position:
            # 有持仓，更新状态
            if state['entry_price'] == 0:
                # 新建仓位
                state['entry_price'] = current_price  # 简化处理
                state['highest_price_since_entry'] = current_price
                state['trailing_stop_price'] = 0
                state['bars_since_entry'] = 0
                print(f"📊 新建仓位状态: 入场价={state['entry_price']:.3f}")
                
            # 更新最高价
            if current_price > state['highest_price_since_entry']:
                state['highest_price_since_entry'] = current_price
                print(f"📈 更新最高价: {current_price:.3f}")
                
            # 更新K线计数
            state['bars_since_entry'] += 1
            
        else:
            # 无持仓，重置状态
            if state['entry_price'] > 0:
                print(f"📊 清空仓位状态")
            state['entry_price'] = 0
            state['highest_price_since_entry'] = 0
            state['trailing_stop_price'] = 0
            state['bars_since_entry'] = 0
            
        state['last_update_time'] = str(datetime.datetime.now())
        
    except Exception as e:
        print(f"❌ 策略状态更新异常: {e}")

def get_strategy_state(ContextInfo, stock_code):
    """
    获取策略状态
    
    Args:
        ContextInfo: QMT上下文对象
        stock_code: 股票代码
        
    Returns:
        dict: 策略状态
    """
    try:
        if not hasattr(ContextInfo, 'strategy_states'):
            return {
                'highest_price_since_entry': 0,
                'trailing_stop_price': 0,
                'entry_price': 0,
                'bars_since_entry': 0
            }
            
        return ContextInfo.strategy_states.get(stock_code, {
            'highest_price_since_entry': 0,
            'trailing_stop_price': 0,
            'entry_price': 0,
            'bars_since_entry': 0
        })
        
    except Exception as e:
        print(f"❌ 获取策略状态异常: {e}")
        return {
            'highest_price_since_entry': 0,
            'trailing_stop_price': 0,
            'entry_price': 0,
            'bars_since_entry': 0
        }

# ============================================================================
# 模块信息
# ============================================================================

__version__ = "1.0.0"
__author__ = "止盈止损模块"
__description__ = """
止盈止损模块

主要功能：
1. ATR动态止损计算
2. 唐奇安通道移动止盈
3. 技术指标计算（MA、ATR、唐奇安通道）
4. 买入信号检测
5. 平仓条件检查
6. 时间风控管理

核心函数：
- calculate_atr: ATR计算
- calculate_donchian_channel: 唐奇安通道计算
- check_buy_signal_three_bars: 买入信号检测
- check_exit_conditions_donchian: 止盈止损检查
- check_time_risk_control: 时间风控检查

使用方法：
在QMT中导入此模块，调用相应函数进行止盈止损判断
"""

if __name__ == "__main__":
    print(__description__)
    print(f"版本: {__version__}")
    print("✅ 止盈止损模块加载完成")
