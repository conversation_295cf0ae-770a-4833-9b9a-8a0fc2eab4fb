#coding:gbk

# 导入常用数据处理和技术分析库
import pandas as pd  # 数据处理库，用于处理时间序列数据
import numpy as np   # 数值计算库，用于数学运算和统计计算
import talib         # 技术分析库，提供各种技术指标计算函数

def init(C):
    """
    策略初始化函数
    基于上涨回调策略的通达信公式实现
    """
    # 构建完整的股票代码，格式：股票代码.市场代码（如：000001.SZ）
    C.stock = C.stockcode + '.' + C.market
    
    # 设置回测账户ID，回测模式下可以使用任意字符串作为账户标识
    C.accountid = "testS"
    
    # 初始化tick数据缓冲区
    C.tick_buffer = []  # 存储待合成的tick数据
    C.last_volume = 0   # 记录上一个累积成交量
    
    # 构建空的DataFrame用于存储合成的K线数据
    C.merged_df = pd.DataFrame(columns=['stime', 'Open', 'High', 'Low', 'Close', 'Volume'])
    C.merged_df.set_index('stime', inplace=True)
    
    # 定义需要计算指标的最小K线数量
    C.min_bars_for_indicators = 80  # 增加到80根以确保指标计算准确
    
    # 策略状态初始化
    C.position = 0  # 持仓状态：0-空仓，1-持多仓
    C.entry_price = 0  # 入场价格
    C.entry_time = None  # 入场时间
    
    # 上涨回调策略参数
    C.strategy_params = {
        # EXPMA参数
        'expma_m1': 25,
        'expma_m2': 70,
        
        # SKDJ参数
        'skdj_n': 7,
        'skdj_m': 4,
        
        # MFI参数
        'mfi_n': 6,
        
        # 阻力线参数
        'resistance_period': 20,
        
        # 止损参数
        'stop_loss_pct': 0.003,  # 0.3%初始止损
        
        # 唐奇安通道参数
        'donchian_period': 20
    }
    
    print(f"📊 上涨回调策略初始化完成")
    print(f"   股票代码: {C.stock}")
    print(f"   策略参数: {C.strategy_params}")

def calculate_expma(closes, m1, m2):
    """
    计算EXPMA指标
    参数：M1 = 25， M2 = 70
    """
    try:
        exp1 = talib.EMA(closes, timeperiod=m1)
        exp2 = talib.EMA(closes, timeperiod=m2)
        return exp1, exp2
    except:
        return np.full(len(closes), np.nan), np.full(len(closes), np.nan)

def calculate_skdj(highs, lows, closes, n, m):
    """
    计算SKDJ指标
    参数（n：7，m：4）
    """
    try:
        lowv = talib.MIN(lows, timeperiod=n)
        highv = talib.MAX(highs, timeperiod=n)
        
        # 计算RSV
        rsv = (closes - lowv) / (highv - lowv) * 100
        rsv = np.where(np.isnan(rsv), 50, rsv)  # 处理除零情况
        
        # 计算K和D
        k = talib.EMA(rsv, timeperiod=m)
        d = talib.SMA(k, timeperiod=m)
        
        return k, d
    except:
        return np.full(len(closes), np.nan), np.full(len(closes), np.nan)

def calculate_mfi(highs, lows, closes, volumes, n):
    """
    计算MFI - 资金流量指数
    参数：N=6
    """
    try:
        # 计算典型价格
        typ = (highs + lows + closes) / 3
        
        # 计算资金流量
        mf = typ * volumes
        
        # 计算正负资金流量
        typ_diff = np.diff(typ, prepend=typ[0])
        positive_mf = np.where(typ_diff > 0, mf, 0)
        negative_mf = np.where(typ_diff < 0, mf, 0)
        
        # 计算N期正负资金流量和
        positive_mf_sum = talib.SUM(positive_mf, timeperiod=n)
        negative_mf_sum = talib.SUM(negative_mf, timeperiod=n)
        
        # 计算MFI
        mfi = 100 - 100 / (1 + positive_mf_sum / (negative_mf_sum + 1e-10))
        
        return mfi
    except:
        return np.full(len(closes), np.nan)

def calculate_resistance_line(highs, lows, closes):
    """
    计算阻力线
    K线的加权均值 = (最高价+最低价+2*收盘价)/4
    阻力线 = K线加权均值 + (K线加权均值 - 最低价)
    """
    try:
        # K线加权均值
        weighted_avg = (highs + lows + 2 * closes) / 4
        
        # 阻力线
        resistance = weighted_avg + (weighted_avg - lows)
        
        return resistance
    except:
        return np.full(len(closes), np.nan)

def calculate_donchian_channel(highs, lows, period):
    """
    计算唐奇安通道
    """
    try:
        upper = talib.MAX(highs, timeperiod=period)
        lower = talib.MIN(lows, timeperiod=period)
        return upper, lower
    except:
        return np.full(len(highs), np.nan), np.full(len(lows), np.nan)

def check_buy_conditions(C, indicators):
    """
    检查买入条件
    买入条件：
    1. EXPMA：EXP1>EXP2
    2. SKDJ的K和D都小于20
    3. MFI小于20
    4. 当价格向上突破阻力线（经典突破确认）
    """
    try:
        exp1, exp2 = indicators['expma']
        k, d = indicators['skdj']
        mfi = indicators['mfi']
        resistance_historical = indicators['resistance_historical']  # 基于历史数据的阻力线
        current_close = indicators['current_close']
        prev_close = indicators['prev_close']

        # 获取最新值（去除NaN）
        exp1_current = exp1[-1] if not np.isnan(exp1[-1]) else 0
        exp2_current = exp2[-1] if not np.isnan(exp2[-1]) else 0
        k_current = k[-1] if not np.isnan(k[-1]) else 50
        d_current = d[-1] if not np.isnan(d[-1]) else 50

        # 获取历史阻力线值（基于上一根K线之前的数据计算）
        resistance_level = resistance_historical[-1] if not np.isnan(resistance_historical[-1]) else current_close

        # 检查MFI最近3根K线内是否有超卖状态
        mfi_recent_3 = mfi[-3:] if len(mfi) >= 3 else mfi
        mfi_oversold_in_recent = any(val < 20 for val in mfi_recent_3 if not np.isnan(val))

        # 检查各项条件
        condition1 = exp1_current > exp2_current  # EXPMA条件
        condition2 = k_current < 20 and d_current < 20  # SKDJ超卖条件
        condition3 = mfi_oversold_in_recent  # MFI最近3根K线内有超卖条件

        # 简化突破确认：当前K线收盘价 > 前20根K线计算的阻力线
        condition4 = current_close > resistance_level

        # 获取MFI最近3根K线的值用于显示
        mfi_values_str = ", ".join([f"{val:.1f}" for val in mfi_recent_3 if not np.isnan(val)])

        print(f"📊 买入条件检查:")
        print(f"   EXPMA: EXP1({exp1_current:.3f}) > EXP2({exp2_current:.3f}) = {condition1}")
        print(f"   SKDJ: K({k_current:.1f}) < 20 且 D({d_current:.1f}) < 20 = {condition2}")
        print(f"   MFI: 最近3根K线[{mfi_values_str}]内有<20 = {condition3}")
        print(f"   突破阻力线: 当前收盘{current_close:.3f} > 上一根K线阻力线{resistance_level:.3f} = {condition4}")

        return condition1 and condition2 and condition3 and condition4

    except Exception as e:
        print(f"❌ 买入条件检查失败: {e}")
        return False

def calculate_dynamic_donchian_periods(highs, lows, closes, atr_period=14):
    """
    计算唐奇安_加速的动态周期
    基于ATR和波动率的动态调整
    """
    try:
        if len(closes) < atr_period:
            return 20, 20  # 默认周期

        # 计算ATR
        atr = talib.ATR(highs, lows, closes, timeperiod=atr_period)
        current_atr = atr[-1] if not np.isnan(atr[-1]) else 0

        # 计算当前波动率
        current_volatility = (current_atr / closes[-1]) * 100 if closes[-1] > 0 else 0

        # 计算历史波动率均值
        volatility_history = []
        for i in range(max(1, len(closes) - atr_period), len(closes)):
            if i > 0 and closes[i] > 0:
                vol = (atr[i] / closes[i]) * 100 if not np.isnan(atr[i]) else 0
                volatility_history.append(vol)

        historical_avg = np.mean(volatility_history) if volatility_history else current_volatility

        # 计算波动率倍数
        volatility_multiplier = current_volatility / historical_avg if historical_avg > 0 else 1

        # 唐奇安_加速参数
        支撑波动系数 = 3.6
        支撑基础系数 = 1.6
        阻力波动系数 = 1
        阻力基础系数 = 2

        # 计算动态周期
        支撑动态周期 = round(atr_period * 支撑基础系数 / (pow(volatility_multiplier, 1.5) * 支撑波动系数))
        阻力动态周期 = round(atr_period * 阻力基础系数 / (pow(volatility_multiplier, 1.5) * 阻力波动系数))

        # 限制周期范围
        支撑动态周期 = max(5, min(50, 支撑动态周期))
        阻力动态周期 = max(5, min(50, 阻力动态周期))

        return 阻力动态周期, 支撑动态周期

    except Exception as e:
        print(f"❌ 动态周期计算失败: {e}")
        return 20, 20

def check_sell_conditions(C, indicators):
    """
    检查卖出条件 - 使用唐奇安_加速的动态出场方式
    卖出：
    1. 初始止损 0.3%（唐奇安通道未开启时的保底止损）
    2. 上涨0.3%后启用动态唐奇安支撑线（LOW <= 周期低点）
    """
    try:
        current_close = indicators['current_close']
        current_low = indicators['current_low']
        highs = indicators['highs']
        lows = indicators['lows']
        closes = indicators['closes']

        # 计算盈亏比例
        if C.entry_price > 0:
            profit_pct = (current_close - C.entry_price) / C.entry_price
        else:
            profit_pct = 0

        # 初始止损条件（0.3%）
        initial_stop_loss = profit_pct < -0.003  # 0.3%止损

        # 判断是否启用唐奇安通道（需要先盈利0.3%）
        donchian_enabled = profit_pct >= 0.003  # 盈利0.3%后启用

        condition1 = initial_stop_loss  # 初始止损条件
        condition2 = False  # 动态支撑线条件

        if donchian_enabled:
            # 计算动态周期
            阻力动态周期, 支撑动态周期 = calculate_dynamic_donchian_periods(highs, lows, closes)

            # 计算动态支撑线（使用前一根K线的数据，避免未来函数）
            if len(lows) >= 支撑动态周期 + 1:
                # 使用前支撑动态周期根K线的最低价作为支撑线
                周期低点 = np.min(lows[-(支撑动态周期+1):-1])
            else:
                周期低点 = np.min(lows[:-1]) if len(lows) > 1 else current_low

            # 检查动态支撑线条件
            condition2 = current_low <= 周期低点

            print(f"📊 卖出条件检查 (唐奇安_加速已启用):")
            print(f"   初始止损: 盈亏{profit_pct*100:.2f}% < -0.3% = {condition1}")
            print(f"   动态支撑: 当前最低{current_low:.3f} <= 周期低点{周期低点:.3f} (周期{支撑动态周期}) = {condition2}")
        else:
            print(f"📊 卖出条件检查 (唐奇安通道未启用):")
            print(f"   初始止损: 盈亏{profit_pct*100:.2f}% < -0.3% = {condition1}")
            print(f"   唐奇安启用条件: 盈利{profit_pct*100:.2f}% >= 0.3% = {donchian_enabled}")

        # 返回卖出决策
        if condition1:
            return True, "初始止损(0.3%)"
        elif condition2:
            return True, f"动态支撑线(周期{支撑动态周期})"
        else:
            return False, ""

    except Exception as e:
        print(f"❌ 卖出条件检查失败: {e}")
        return False, "检查失败"

def handlebar(C):
    """
    策略主函数 - 每根K线调用
    """
    # 获取当前K线时间信息
    bar_date = timetag_to_datetime(C.get_bar_timetag(C.barpos), '%Y%m%d%H%M%S')
    
    # 获取当前tick数据
    hist_data = C.get_market_data_ex(
        [],  # 获取所有字段
        [C.stock],
        period=C.period,
        end_time=bar_date,
        count=1,  # 回测中取每次最新的1个数据
        dividend_type='none'  # tick数据不需要复权
    )
    
    # 提取价格和成交量数据
    price = hist_data[C.stock][['lastPrice', 'volume']]
    
    # 处理当前tick数据并合成K线
    current_tick = price.iloc[0]
    current_time = pd.to_datetime(bar_date, format='%Y%m%d%H%M%S')
    current_price = current_tick['lastPrice']
    current_volume_cumulative = current_tick['volume']
    
    # 计算增量成交量
    volume_increment = current_volume_cumulative - C.last_volume
    C.last_volume = current_volume_cumulative
    
    # 创建tick数据字典
    tick_data = {
        'time': current_time,
        'price': current_price,
        'volume': volume_increment
    }
    
    # 添加到缓冲区
    C.tick_buffer.append(tick_data)
    
    # 当缓冲区有2个tick数据时，进行合成
    if len(C.tick_buffer) >= 2:
        # 合成新的K线
        new_kline = {
            'stime': C.tick_buffer[0]['time'],
            'Open': C.tick_buffer[0]['price'],
            'High': max(C.tick_buffer[0]['price'], C.tick_buffer[1]['price']),
            'Low': min(C.tick_buffer[0]['price'], C.tick_buffer[1]['price']),
            'Close': C.tick_buffer[1]['price'],
            'Volume': C.tick_buffer[0]['volume'] + C.tick_buffer[1]['volume']
        }
        
        # 添加到merged_df
        new_row = pd.DataFrame([new_kline])
        new_row.set_index('stime', inplace=True)
        C.merged_df = pd.concat([C.merged_df, new_row])
        
        # 清空缓冲区
        C.tick_buffer = []
        
        # 限制数据长度，保持固定窗口
        if len(C.merged_df) > 200:
            C.merged_df = C.merged_df.tail(200)
        
        print(f"📈 {bar_date} 新合成K线: 开{new_kline['Open']:.3f} 高{new_kline['High']:.3f} 低{new_kline['Low']:.3f} 收{new_kline['Close']:.3f}")
        
        # 判断是否有足够的K线进行指标计算和交易
        if len(C.merged_df) >= C.min_bars_for_indicators:
            execute_strategy(C, bar_date)

def execute_strategy(C, bar_date):
    """
    执行上涨回调策略逻辑
    """
    try:
        # 提取OHLCV数据
        df = C.merged_df
        opens = df['Open'].values
        highs = df['High'].values
        lows = df['Low'].values
        closes = df['Close'].values
        volumes = df['Volume'].values
        
        # 计算技术指标
        exp1, exp2 = calculate_expma(closes, C.strategy_params['expma_m1'], C.strategy_params['expma_m2'])
        k, d = calculate_skdj(highs, lows, closes, C.strategy_params['skdj_n'], C.strategy_params['skdj_m'])
        mfi = calculate_mfi(highs, lows, closes, volumes, C.strategy_params['mfi_n'])

        # 计算上一根K线的阻力线
        if len(closes) > 1:
            # 使用上一根K线的数据计算阻力线
            resistance_historical = calculate_resistance_line(
                highs[-2:-1],
                lows[-2:-1],
                closes[-2:-1]
            )
        else:
            # 如果数据不足，使用当前K线数据
            resistance_historical = np.array([closes[-1]])

        donchian_upper, donchian_lower = calculate_donchian_channel(highs, lows, C.strategy_params['donchian_period'])

        # 准备指标数据
        indicators = {
            'expma': (exp1, exp2),
            'skdj': (k, d),
            'mfi': mfi,
            'resistance_historical': resistance_historical,  # 使用历史阻力线
            'donchian_lower': donchian_lower,
            'current_close': closes[-1],
            'current_low': lows[-1],  # 添加当前最低价
            'prev_close': closes[-2] if len(closes) > 1 else closes[-1],
            'highs': highs,  # 添加完整的高价数组
            'lows': lows,    # 添加完整的低价数组
            'closes': closes # 添加完整的收盘价数组
        }
        
        print(f"\n🔍 {bar_date} 上涨回调策略分析:")
        print(f"   当前价格: {closes[-1]:.3f}")
        print(f"   持仓状态: {'持多仓' if C.position == 1 else '空仓'}")
        
        # 交易逻辑
        if C.position == 0:  # 空仓时检查买入条件
            if check_buy_conditions(C, indicators):
                # 执行买入
                C.position = 1
                C.entry_price = closes[-1]
                C.entry_time = bar_date
                print(f"🟢 买入信号触发！")
                print(f"   买入价格: {C.entry_price:.3f}")
                print(f"   买入时间: {C.entry_time}")
                
                # 这里可以添加实际的买入订单代码
                passorder(23, 1101, C.accountid, C.stock, 5, 1, 100, C)  # 买入100股
                print("🟢 执行买入操作")
                C.draw_text(1, 1, '买')
        elif C.position == 1:  # 持仓时检查卖出条件
            should_sell, sell_reason = check_sell_conditions(C, indicators)
            if should_sell:
                # 执行卖出
                profit_pct = (closes[-1] - C.entry_price) / C.entry_price
                print(f"🔴 卖出信号触发！原因: {sell_reason}")
                print(f"   卖出价格: {closes[-1]:.3f}")
                print(f"   持仓收益: {profit_pct*100:.2f}%")
                
                # 重置持仓状态
                C.position = 0
                C.entry_price = 0
                C.entry_time = None
                
                # 这里可以添加实际的卖出订单代码
                holding_vol = 100  # 获取实际持仓数量
                passorder(24, 1101, C.accountid, C.stock, 5, -1, holding_vol, C)  # 卖出持仓
                print("🔴 执行卖出操作")
                C.draw_text(1, 1, '平')
    except Exception as e:
        print(f"❌ 策略执行失败: {e}")
